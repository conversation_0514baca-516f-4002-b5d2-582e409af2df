{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue", "mtime": 1751517278069}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750933729645}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750933730285}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnT3BpbmlvbkFuYWx5c2lzJywKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy8g6aG16Z2i5pWw5o2u5bCG5Zyo6L+Z6YeM5a6a5LmJCiAgICB9CiAgfSwKICBtb3VudGVkKCkgewogICAgLy8g6aG16Z2i5Yid5aeL5YyW6YC76L6RCiAgICBjb25zb2xlLmxvZygn6IiG5oOF5YiG5p6Q6aG16Z2i5bey5Yqg6L29JykKICB9LAogIG1ldGhvZHM6IHsKICAgIC8vIOmhtemdouaWueazleWwhuWcqOi/memHjOWumuS5iQogIH0KfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAkFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/opinion-analysis", "sourcesContent": ["<template>\n  <div class=\"opinion-analysis\">\n    <!-- 页面头部 -->\n    <div class=\"page-header\">\n      <div class=\"header-content\">\n        <h1 class=\"page-title\">\n          <i class=\"el-icon-data-analysis\"></i>\n          舆情分析\n        </h1>\n        <p class=\"page-description\">深度分析舆情数据，提供全面的舆情洞察</p>\n      </div>\n    </div>\n\n    <!-- 主要内容区域 -->\n    <div class=\"main-content\">\n      <!-- 功能卡片区域 -->\n      <div class=\"feature-cards\">\n        <el-row :gutter=\"24\">\n          <el-col :span=\"8\">\n            <div class=\"feature-card\">\n              <div class=\"card-icon\">\n                <i class=\"el-icon-pie-chart\"></i>\n              </div>\n              <div class=\"card-content\">\n                <h3>情感分析</h3>\n                <p>分析舆情数据的情感倾向，识别正面、负面和中性情绪</p>\n              </div>\n            </div>\n          </el-col>\n          <el-col :span=\"8\">\n            <div class=\"feature-card\">\n              <div class=\"card-icon\">\n                <i class=\"el-icon-trend-charts\"></i>\n              </div>\n              <div class=\"card-content\">\n                <h3>趋势分析</h3>\n                <p>追踪舆情发展趋势，预测未来走向</p>\n              </div>\n            </div>\n          </el-col>\n          <el-col :span=\"8\">\n            <div class=\"feature-card\">\n              <div class=\"card-icon\">\n                <i class=\"el-icon-location\"></i>\n              </div>\n              <div class=\"card-content\">\n                <h3>地域分析</h3>\n                <p>分析不同地区的舆情分布和特征</p>\n              </div>\n            </div>\n          </el-col>\n        </el-row>\n      </div>\n\n      <!-- 占位内容区域 -->\n      <div class=\"placeholder-content\">\n        <el-card class=\"content-card\">\n          <div slot=\"header\" class=\"card-header\">\n            <span>分析工具</span>\n            <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\">新建分析</el-button>\n          </div>\n          \n          <div class=\"empty-state\">\n            <div class=\"empty-icon\">\n              <i class=\"el-icon-data-analysis\"></i>\n            </div>\n            <h3>舆情分析功能开发中</h3>\n            <p>我们正在为您准备强大的舆情分析工具</p>\n            <div class=\"coming-features\">\n              <el-tag type=\"info\" class=\"feature-tag\">智能情感识别</el-tag>\n              <el-tag type=\"info\" class=\"feature-tag\">关键词提取</el-tag>\n              <el-tag type=\"info\" class=\"feature-tag\">影响力评估</el-tag>\n              <el-tag type=\"info\" class=\"feature-tag\">传播路径分析</el-tag>\n            </div>\n          </div>\n        </el-card>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'OpinionAnalysis',\n  data() {\n    return {\n      // 页面数据将在这里定义\n    }\n  },\n  mounted() {\n    // 页面初始化逻辑\n    console.log('舆情分析页面已加载')\n  },\n  methods: {\n    // 页面方法将在这里定义\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.opinion-analysis {\n  padding: 0;\n  background-color: #f5f5f5;\n  min-height: 100vh;\n}\n\n.page-header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 40px 24px;\n  margin-bottom: 24px;\n\n  .header-content {\n    max-width: 1200px;\n    margin: 0 auto;\n\n    .page-title {\n      font-size: 32px;\n      font-weight: 600;\n      margin: 0 0 12px 0;\n      display: flex;\n      align-items: center;\n\n      i {\n        margin-right: 12px;\n        font-size: 36px;\n      }\n    }\n\n    .page-description {\n      font-size: 16px;\n      opacity: 0.9;\n      margin: 0;\n    }\n  }\n}\n\n.main-content {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 24px;\n}\n\n.feature-cards {\n  margin-bottom: 32px;\n\n  .feature-card {\n    background: white;\n    border-radius: 12px;\n    padding: 32px 24px;\n    text-align: center;\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n    transition: all 0.3s ease;\n    height: 200px;\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n\n    &:hover {\n      transform: translateY(-4px);\n      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\n    }\n\n    .card-icon {\n      margin-bottom: 16px;\n\n      i {\n        font-size: 48px;\n        color: #667eea;\n      }\n    }\n\n    .card-content {\n      h3 {\n        font-size: 18px;\n        font-weight: 600;\n        margin: 0 0 8px 0;\n        color: #333;\n      }\n\n      p {\n        font-size: 14px;\n        color: #666;\n        margin: 0;\n        line-height: 1.5;\n      }\n    }\n  }\n}\n\n.placeholder-content {\n  .content-card {\n    border-radius: 12px;\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n\n    .card-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      font-weight: 600;\n      font-size: 16px;\n    }\n  }\n}\n\n.empty-state {\n  text-align: center;\n  padding: 60px 20px;\n\n  .empty-icon {\n    margin-bottom: 24px;\n\n    i {\n      font-size: 80px;\n      color: #d9d9d9;\n    }\n  }\n\n  h3 {\n    font-size: 20px;\n    color: #333;\n    margin: 0 0 12px 0;\n    font-weight: 600;\n  }\n\n  p {\n    font-size: 16px;\n    color: #666;\n    margin: 0 0 32px 0;\n  }\n\n  .coming-features {\n    display: flex;\n    justify-content: center;\n    flex-wrap: wrap;\n    gap: 12px;\n\n    .feature-tag {\n      font-size: 14px;\n      padding: 8px 16px;\n      border-radius: 20px;\n    }\n  }\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  .page-header {\n    padding: 24px 16px;\n\n    .page-title {\n      font-size: 24px;\n\n      i {\n        font-size: 28px;\n      }\n    }\n\n    .page-description {\n      font-size: 14px;\n    }\n  }\n\n  .main-content {\n    padding: 0 16px;\n  }\n\n  .feature-cards {\n    .el-col {\n      margin-bottom: 16px;\n    }\n\n    .feature-card {\n      height: auto;\n      padding: 24px 16px;\n    }\n  }\n}\n</style>\n"]}]}