{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue", "mtime": 1751520809838}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750933729645}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750933730285}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAgFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/opinion-analysis", "sourcesContent": ["<template>\n  <div class=\"opinion-analysis\">\n    <!-- 3D头像 -->\n    <div class=\"avatar-container\">\n      <div class=\"avatar-3d\">\n        <div class=\"avatar-face\">😊</div>\n        <div class=\"avatar-greeting\">hi</div>\n      </div>\n    </div>\n\n    <!-- 主要聊天界面 -->\n    <div class=\"chat-container\">\n      <!-- 问候语 -->\n      <div class=\"greeting-section\">\n        <h1 class=\"greeting-text\">你好，{{ userName }}</h1>\n      </div>\n\n      <!-- 聊天输入区域 -->\n      <div class=\"chat-input-section\">\n        <div class=\"chat-input-container\">\n          <div class=\"input-header\">\n            <span class=\"time-info\">{{ timeInfo }}</span>\n            <span class=\"status-info\">{{ statusInfo }}</span>\n          </div>\n\n          <div class=\"input-wrapper\">\n            <el-input\n              v-model=\"inputMessage\"\n              type=\"textarea\"\n              :rows=\"3\"\n              :placeholder=\"placeholderText\"\n              class=\"chat-input\"\n              @keyup.enter.ctrl=\"sendMessage\"\n            />\n          </div>\n\n          <!-- 功能按钮区域 -->\n          <div class=\"function-buttons\">\n            <div class=\"left-buttons\">\n              <el-button\n                type=\"text\"\n                icon=\"el-icon-paperclip\"\n                class=\"function-btn\"\n                @click=\"handleAttachment\">\n                附件\n              </el-button>\n              <el-button\n                type=\"text\"\n                icon=\"el-icon-s-grid\"\n                class=\"function-btn\"\n                @click=\"handleExtension\">\n                扩展\n              </el-button>\n              <el-button\n                type=\"text\"\n                icon=\"el-icon-magic-stick\"\n                class=\"function-btn\"\n                @click=\"handleAuto\">\n                自动\n              </el-button>\n            </div>\n\n            <div class=\"right-buttons\">\n              <el-button\n                type=\"primary\"\n                icon=\"el-icon-s-promotion\"\n                class=\"send-btn\"\n                @click=\"sendMessage\"\n                :disabled=\"!inputMessage.trim()\">\n                发送\n              </el-button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'OpinionAnalysis',\n  data() {\n    return {\n      userName: '',\n      inputMessage: '',\n      currentTime: new Date(),\n      placeholderText: '请输入您的舆情分析需求...'\n    }\n  },\n  computed: {\n    timeInfo() {\n      const now = this.currentTime\n      const today = now.toLocaleDateString('zh-CN', {\n        month: 'long',\n        day: 'numeric',\n        weekday: 'long'\n      })\n      const time = now.toLocaleTimeString('zh-CN', {\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: false\n      })\n      return `${today}，比如：上午 ${time}`\n    },\n    statusInfo() {\n      return '我我：在等待您，比如：舆情AI助手'\n    }\n  },\n  mounted() {\n    // 页面初始化逻辑\n    console.log('舆情分析聊天界面已加载')\n\n    // 获取用户信息\n    this.getUserInfo()\n\n    // 每分钟更新时间\n    this.timeInterval = setInterval(() => {\n      this.currentTime = new Date()\n    }, 60000)\n  },\n  beforeDestroy() {\n    // 清理定时器\n    if (this.timeInterval) {\n      clearInterval(this.timeInterval)\n    }\n  },\n  methods: {\n    // 获取用户信息\n    getUserInfo() {\n      // 从Vuex store中获取用户信息\n      const userInfo = this.$store.state.user.userInfo\n      if (userInfo && userInfo.userName) {\n        this.userName = userInfo.userName\n      } else {\n        // 如果没有用户信息，尝试从localStorage获取\n        const storedUserInfo = localStorage.getItem('userInfo')\n        if (storedUserInfo) {\n          try {\n            const parsedUserInfo = JSON.parse(storedUserInfo)\n            this.userName = parsedUserInfo.userName || '用户'\n          } catch (e) {\n            this.userName = '用户'\n          }\n        } else {\n          this.userName = '用户'\n        }\n      }\n    },\n\n    // 发送消息\n    sendMessage() {\n      if (!this.inputMessage.trim()) return\n\n      console.log('发送消息:', this.inputMessage)\n      // 这里可以添加发送消息的逻辑\n      this.$message.success('消息已发送，舆情分析功能开发中...')\n      this.inputMessage = ''\n    },\n\n    // 处理附件\n    handleAttachment() {\n      this.$message.info('附件功能开发中...')\n    },\n\n    // 处理扩展\n    handleExtension() {\n      this.$message.info('扩展功能开发中...')\n    },\n\n    // 处理自动功能\n    handleAuto() {\n      this.$message.info('自动功能开发中...')\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.opinion-analysis {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  padding: 20px;\n}\n\n// 3D头像样式\n.avatar-container {\n  position: absolute;\n  top: 60px;\n  right: 80px;\n  z-index: 10;\n\n  .avatar-3d {\n    position: relative;\n    width: 80px;\n    height: 80px;\n\n    .avatar-face {\n      width: 80px;\n      height: 80px;\n      background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 32px;\n      box-shadow: 0 8px 32px rgba(255, 154, 158, 0.3);\n      animation: float 3s ease-in-out infinite;\n    }\n\n    .avatar-greeting {\n      position: absolute;\n      top: -10px;\n      left: -20px;\n      background: white;\n      color: #666;\n      padding: 4px 12px;\n      border-radius: 20px;\n      font-size: 14px;\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n      animation: bounce 2s ease-in-out infinite;\n    }\n  }\n}\n\n// 主聊天容器\n.chat-container {\n  width: 100%;\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n// 问候语区域\n.greeting-section {\n  text-align: center;\n  margin-bottom: 60px;\n\n  .greeting-text {\n    font-size: 36px;\n    font-weight: 300;\n    color: #333;\n    margin: 0;\n    letter-spacing: 1px;\n  }\n}\n\n// 聊天输入区域\n.chat-input-section {\n  .chat-input-container {\n    background: white;\n    border-radius: 24px;\n    padding: 24px;\n    box-shadow: 0 8px 40px rgba(0, 0, 0, 0.12);\n    border: 1px solid rgba(0, 0, 0, 0.05);\n\n    .input-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 16px;\n      font-size: 14px;\n      color: #666;\n\n      .time-info {\n        color: #999;\n      }\n\n      .status-info {\n        color: #666;\n      }\n    }\n\n    .input-wrapper {\n      margin-bottom: 16px;\n\n      .chat-input {\n        ::v-deep .el-textarea__inner {\n          border: none;\n          background: #f8f9fa;\n          border-radius: 12px;\n          padding: 16px;\n          font-size: 16px;\n          line-height: 1.5;\n          resize: none;\n          box-shadow: none;\n\n          &:focus {\n            background: #f0f2f5;\n            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);\n          }\n\n          &::placeholder {\n            color: #bbb;\n          }\n        }\n      }\n    }\n\n    .function-buttons {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n\n      .left-buttons {\n        display: flex;\n        gap: 8px;\n\n        .function-btn {\n          color: #666;\n          font-size: 14px;\n          padding: 8px 16px;\n          border-radius: 20px;\n          transition: all 0.3s ease;\n\n          &:hover {\n            background: #f0f2f5;\n            color: #409eff;\n          }\n\n          i {\n            margin-right: 4px;\n          }\n        }\n      }\n\n      .right-buttons {\n        .send-btn {\n          padding: 10px 24px;\n          border-radius: 20px;\n          font-weight: 500;\n          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);\n\n          &:not(:disabled):hover {\n            transform: translateY(-1px);\n            box-shadow: 0 6px 16px rgba(64, 158, 255, 0.4);\n          }\n        }\n      }\n    }\n  }\n}\n\n// 动画效果\n@keyframes float {\n  0%, 100% {\n    transform: translateY(0px);\n  }\n  50% {\n    transform: translateY(-10px);\n  }\n}\n\n@keyframes bounce {\n  0%, 100% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.1);\n  }\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  .opinion-analysis {\n    padding: 16px;\n  }\n\n  .avatar-container {\n    top: 30px;\n    right: 30px;\n\n    .avatar-3d {\n      width: 60px;\n      height: 60px;\n\n      .avatar-face {\n        width: 60px;\n        height: 60px;\n        font-size: 24px;\n      }\n\n      .avatar-greeting {\n        font-size: 12px;\n        padding: 2px 8px;\n      }\n    }\n  }\n\n  .greeting-section {\n    margin-bottom: 40px;\n\n    .greeting-text {\n      font-size: 28px;\n    }\n  }\n\n  .chat-input-section {\n    .chat-input-container {\n      padding: 20px;\n      border-radius: 20px;\n\n      .function-buttons {\n        flex-direction: column;\n        gap: 12px;\n\n        .left-buttons {\n          justify-content: center;\n        }\n\n        .right-buttons {\n          width: 100%;\n\n          .send-btn {\n            width: 100%;\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}