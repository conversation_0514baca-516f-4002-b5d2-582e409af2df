<template>
  <div class="opinion-analysis">
    <!-- 3D头像 -->
    <div class="avatar-container">
      <div class="avatar-3d">
        <div class="avatar-face">😊</div>
        <div class="avatar-greeting">hi</div>
      </div>
    </div>

    <!-- 主要聊天界面 -->
    <div class="chat-container">
      <!-- 问候语 -->
      <div class="greeting-section">
        <h1 class="greeting-text">你好，{{ userName }}</h1>
      </div>

      <!-- 聊天输入区域 -->
      <div class="chat-input-section">
        <div class="chat-input-container">
          <div class="input-header">
            <span class="time-info">{{ timeInfo }}</span>
            <span class="status-info">{{ statusInfo }}</span>
          </div>

          <div class="input-wrapper">
            <el-input
              v-model="inputMessage"
              type="textarea"
              :rows="3"
              :placeholder="placeholderText"
              class="chat-input"
              @keyup.enter.ctrl="sendMessage"
            />
          </div>

          <!-- 功能按钮区域 -->
          <div class="function-buttons">
            <div class="left-buttons">
              <el-button
                type="text"
                icon="el-icon-paperclip"
                class="function-btn"
                @click="handleAttachment">
                附件
              </el-button>
              <el-button
                type="text"
                icon="el-icon-s-grid"
                class="function-btn"
                @click="handleExtension">
                扩展
              </el-button>
              <el-button
                type="text"
                icon="el-icon-magic-stick"
                class="function-btn"
                @click="handleAuto">
                自动
              </el-button>
            </div>

            <div class="right-buttons">
              <el-button
                type="primary"
                icon="el-icon-s-promotion"
                class="send-btn"
                @click="sendMessage"
                :disabled="!inputMessage.trim()">
                发送
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'OpinionAnalysis',
  data() {
    return {
      userName: 'RootUser_2108572754',
      inputMessage: '',
      currentTime: new Date(),
      placeholderText: '请输入您的舆情分析需求...'
    }
  },
  computed: {
    timeInfo() {
      const now = this.currentTime
      const today = now.toLocaleDateString('zh-CN', {
        month: 'long',
        day: 'numeric',
        weekday: 'long'
      })
      const time = now.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      })
      return `${today}，比如：上午 ${time}`
    },
    statusInfo() {
      return '我我：在等待您，比如：舆情AI助手'
    }
  },
  mounted() {
    // 页面初始化逻辑
    console.log('舆情分析聊天界面已加载')
    // 每分钟更新时间
    this.timeInterval = setInterval(() => {
      this.currentTime = new Date()
    }, 60000)
  },
  beforeDestroy() {
    // 清理定时器
    if (this.timeInterval) {
      clearInterval(this.timeInterval)
    }
  },
  methods: {
    // 发送消息
    sendMessage() {
      if (!this.inputMessage.trim()) return

      console.log('发送消息:', this.inputMessage)
      // 这里可以添加发送消息的逻辑
      this.$message.success('消息已发送，舆情分析功能开发中...')
      this.inputMessage = ''
    },

    // 处理附件
    handleAttachment() {
      this.$message.info('附件功能开发中...')
    },

    // 处理扩展
    handleExtension() {
      this.$message.info('扩展功能开发中...')
    },

    // 处理自动功能
    handleAuto() {
      this.$message.info('自动功能开发中...')
    }
  }
}
</script>

<style lang="scss" scoped>
.opinion-analysis {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 20px;
}

// 3D头像样式
.avatar-container {
  position: absolute;
  top: 60px;
  right: 80px;
  z-index: 10;

  .avatar-3d {
    position: relative;
    width: 80px;
    height: 80px;

    .avatar-face {
      width: 80px;
      height: 80px;
      background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 32px;
      box-shadow: 0 8px 32px rgba(255, 154, 158, 0.3);
      animation: float 3s ease-in-out infinite;
    }

    .avatar-greeting {
      position: absolute;
      top: -10px;
      left: -20px;
      background: white;
      color: #666;
      padding: 4px 12px;
      border-radius: 20px;
      font-size: 14px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      animation: bounce 2s ease-in-out infinite;
    }
  }
}

// 主聊天容器
.chat-container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

// 问候语区域
.greeting-section {
  text-align: center;
  margin-bottom: 60px;

  .greeting-text {
    font-size: 36px;
    font-weight: 300;
    color: #333;
    margin: 0;
    letter-spacing: 1px;
  }
}

// 聊天输入区域
.chat-input-section {
  .chat-input-container {
    background: white;
    border-radius: 24px;
    padding: 24px;
    box-shadow: 0 8px 40px rgba(0, 0, 0, 0.12);
    border: 1px solid rgba(0, 0, 0, 0.05);

    .input-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      font-size: 14px;
      color: #666;

      .time-info {
        color: #999;
      }

      .status-info {
        color: #666;
      }
    }

    .input-wrapper {
      margin-bottom: 16px;

      .chat-input {
        ::v-deep .el-textarea__inner {
          border: none;
          background: #f8f9fa;
          border-radius: 12px;
          padding: 16px;
          font-size: 16px;
          line-height: 1.5;
          resize: none;
          box-shadow: none;

          &:focus {
            background: #f0f2f5;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
          }

          &::placeholder {
            color: #bbb;
          }
        }
      }
    }

    .function-buttons {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .left-buttons {
        display: flex;
        gap: 8px;

        .function-btn {
          color: #666;
          font-size: 14px;
          padding: 8px 16px;
          border-radius: 20px;
          transition: all 0.3s ease;

          &:hover {
            background: #f0f2f5;
            color: #409eff;
          }

          i {
            margin-right: 4px;
          }
        }
      }

      .right-buttons {
        .send-btn {
          padding: 10px 24px;
          border-radius: 20px;
          font-weight: 500;
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);

          &:not(:disabled):hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 16px rgba(64, 158, 255, 0.4);
          }
        }
      }
    }
  }
}

// 动画效果
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes bounce {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .opinion-analysis {
    padding: 16px;
  }

  .avatar-container {
    top: 30px;
    right: 30px;

    .avatar-3d {
      width: 60px;
      height: 60px;

      .avatar-face {
        width: 60px;
        height: 60px;
        font-size: 24px;
      }

      .avatar-greeting {
        font-size: 12px;
        padding: 2px 8px;
      }
    }
  }

  .greeting-section {
    margin-bottom: 40px;

    .greeting-text {
      font-size: 28px;
    }
  }

  .chat-input-section {
    .chat-input-container {
      padding: 20px;
      border-radius: 20px;

      .function-buttons {
        flex-direction: column;
        gap: 12px;

        .left-buttons {
          justify-content: center;
        }

        .right-buttons {
          width: 100%;

          .send-btn {
            width: 100%;
          }
        }
      }
    }
  }
}
</style>
