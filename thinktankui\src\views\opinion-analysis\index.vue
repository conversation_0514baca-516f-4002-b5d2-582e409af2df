<template>
  <div class="opinion-analysis">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <i class="el-icon-data-analysis"></i>
          舆情分析
        </h1>
        <p class="page-description">深度分析舆情数据，提供全面的舆情洞察</p>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 功能卡片区域 -->
      <div class="feature-cards">
        <el-row :gutter="24">
          <el-col :span="8">
            <div class="feature-card">
              <div class="card-icon">
                <i class="el-icon-pie-chart"></i>
              </div>
              <div class="card-content">
                <h3>情感分析</h3>
                <p>分析舆情数据的情感倾向，识别正面、负面和中性情绪</p>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="feature-card">
              <div class="card-icon">
                <i class="el-icon-trend-charts"></i>
              </div>
              <div class="card-content">
                <h3>趋势分析</h3>
                <p>追踪舆情发展趋势，预测未来走向</p>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="feature-card">
              <div class="card-icon">
                <i class="el-icon-location"></i>
              </div>
              <div class="card-content">
                <h3>地域分析</h3>
                <p>分析不同地区的舆情分布和特征</p>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 占位内容区域 -->
      <div class="placeholder-content">
        <el-card class="content-card">
          <div slot="header" class="card-header">
            <span>分析工具</span>
            <el-button type="primary" size="small" icon="el-icon-plus">新建分析</el-button>
          </div>
          
          <div class="empty-state">
            <div class="empty-icon">
              <i class="el-icon-data-analysis"></i>
            </div>
            <h3>舆情分析功能开发中</h3>
            <p>我们正在为您准备强大的舆情分析工具</p>
            <div class="coming-features">
              <el-tag type="info" class="feature-tag">智能情感识别</el-tag>
              <el-tag type="info" class="feature-tag">关键词提取</el-tag>
              <el-tag type="info" class="feature-tag">影响力评估</el-tag>
              <el-tag type="info" class="feature-tag">传播路径分析</el-tag>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'OpinionAnalysis',
  data() {
    return {
      // 页面数据将在这里定义
    }
  },
  mounted() {
    // 页面初始化逻辑
    console.log('舆情分析页面已加载')
  },
  methods: {
    // 页面方法将在这里定义
  }
}
</script>

<style lang="scss" scoped>
.opinion-analysis {
  padding: 0;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 40px 24px;
  margin-bottom: 24px;

  .header-content {
    max-width: 1200px;
    margin: 0 auto;

    .page-title {
      font-size: 32px;
      font-weight: 600;
      margin: 0 0 12px 0;
      display: flex;
      align-items: center;

      i {
        margin-right: 12px;
        font-size: 36px;
      }
    }

    .page-description {
      font-size: 16px;
      opacity: 0.9;
      margin: 0;
    }
  }
}

.main-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.feature-cards {
  margin-bottom: 32px;

  .feature-card {
    background: white;
    border-radius: 12px;
    padding: 32px 24px;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: center;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    }

    .card-icon {
      margin-bottom: 16px;

      i {
        font-size: 48px;
        color: #667eea;
      }
    }

    .card-content {
      h3 {
        font-size: 18px;
        font-weight: 600;
        margin: 0 0 8px 0;
        color: #333;
      }

      p {
        font-size: 14px;
        color: #666;
        margin: 0;
        line-height: 1.5;
      }
    }
  }
}

.placeholder-content {
  .content-card {
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 600;
      font-size: 16px;
    }
  }
}

.empty-state {
  text-align: center;
  padding: 60px 20px;

  .empty-icon {
    margin-bottom: 24px;

    i {
      font-size: 80px;
      color: #d9d9d9;
    }
  }

  h3 {
    font-size: 20px;
    color: #333;
    margin: 0 0 12px 0;
    font-weight: 600;
  }

  p {
    font-size: 16px;
    color: #666;
    margin: 0 0 32px 0;
  }

  .coming-features {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 12px;

    .feature-tag {
      font-size: 14px;
      padding: 8px 16px;
      border-radius: 20px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .page-header {
    padding: 24px 16px;

    .page-title {
      font-size: 24px;

      i {
        font-size: 28px;
      }
    }

    .page-description {
      font-size: 14px;
    }
  }

  .main-content {
    padding: 0 16px;
  }

  .feature-cards {
    .el-col {
      margin-bottom: 16px;
    }

    .feature-card {
      height: auto;
      padding: 24px 16px;
    }
  }
}
</style>
