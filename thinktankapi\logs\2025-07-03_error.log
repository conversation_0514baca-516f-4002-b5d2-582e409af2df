2025-07-03 09:29:09.349 |  | INFO     | server:lifespan:62 - RuoYi-FastAPI开始启动
2025-07-03 09:29:09.349 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-03 11:24:38.082 |  | INFO     | server:lifespan:62 - RuoYi-FastAPI开始启动
2025-07-03 11:24:38.083 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-03 11:24:39.216 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-03 11:24:39.217 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-03 11:24:39.230 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-03 11:24:39.828 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-03 11:24:40.481 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-03 11:24:40.483 |  | INFO     | server:lifespan:69 - RuoYi-FastAPI启动成功
2025-07-03 11:25:21.473 | f285ba29bd5b485b8f276de27b7c7f35 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为af6b4c9b-17ef-420d-af8b-68e8ae5f89c3的会话获取图片验证码成功
2025-07-03 11:25:28.193 | 79146a8ff6a34cb0a93021bff4ff748d | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-07-03 11:25:28.420 | 998437e8813b421eae1f791ff04f474b | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 11:25:28.916 | def86463716745ccad5ccba166d50d3a | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 11:25:32.966 | 6db0b8e7794248f4876ad86592242504 | INFO     | module_admin.controller.keyword_data_controller:get_web_statistics_public:273 - 获取网站统计数据成功
2025-07-03 11:25:33.252 | f4d496fbfefc4eeb8f459f1fbb43a094 | INFO     | module_admin.controller.keyword_data_controller:get_keyword_data_list_public:174 - 查询参数: pageNum=1, pageSize=10, timeFilter=None, actual_start_date=None, actual_end_date=None
2025-07-03 11:25:33.253 | f4d496fbfefc4eeb8f459f1fbb43a094 | INFO     | module_admin.service.keyword_data_service:get_keyword_data_list_services:40 - Service层接收到的查询参数: begin_time=None, end_time=None, time_filter=None
2025-07-03 11:25:33.253 | f4d496fbfefc4eeb8f459f1fbb43a094 | INFO     | module_admin.service.keyword_data_service:get_keyword_data_list_services:41 - Service层查询对象类型: <class 'module_admin.entity.vo.keyword_data_vo.KeywordDataPageQueryModel'>
2025-07-03 11:25:33.253 | f4d496fbfefc4eeb8f459f1fbb43a094 | INFO     | module_admin.service.keyword_data_service:get_keyword_data_list_services:42 - Service层查询对象属性: {'page_num': 1, 'page_size': 10, 'title': None, 'keyword': None, 'type': None, 'web': None, 'begin_time': None, 'end_time': None, 'platform_types': None, 'sentiment_types': None, 'info_attributes': None, 'web_site': None, 'time_filter': None}
2025-07-03 11:25:33.254 | f4d496fbfefc4eeb8f459f1fbb43a094 | INFO     | module_admin.dao.keyword_data_dao:get_keyword_data_list:112 - DAO层时间筛选参数: begin_time=None, end_time=None, time_filter=None
2025-07-03 11:25:33.254 | f4d496fbfefc4eeb8f459f1fbb43a094 | INFO     | module_admin.dao.keyword_data_dao:get_keyword_data_list:182 - 总共构建了 0 个查询条件
2025-07-03 11:25:33.254 | f4d496fbfefc4eeb8f459f1fbb43a094 | INFO     | module_admin.dao.keyword_data_dao:get_keyword_data_list:183 - 查询参数: time_filter=None, platform_types=None, sentiment_types=None
2025-07-03 11:25:33.255 | f4d496fbfefc4eeb8f459f1fbb43a094 | INFO     | module_admin.dao.keyword_data_dao:get_keyword_data_list:189 - 没有查询条件，将返回所有数据
2025-07-03 11:25:33.271 | 6f30194d58864ac1bcecdb76ac790869 | INFO     | module_admin.controller.keyword_data_controller:get_filter_statistics_public:256 - 获取筛选统计数据成功
2025-07-03 11:25:33.313 | f4d496fbfefc4eeb8f459f1fbb43a094 | INFO     | module_admin.controller.keyword_data_controller:get_keyword_data_list_public:178 - 查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-03 11:25:33.314 | f4d496fbfefc4eeb8f459f1fbb43a094 | INFO     | module_admin.controller.keyword_data_controller:get_keyword_data_list_public:179 - 查询结果: records=[KeywordDataResponseModel(id=1606, title='多重因素压制 棕榈油进入下行周期', content='在尝试采集网页 `http://futures.eastmoney.com/a/202505123401818734.html` 的正文内容时，遇到了一些问题。具体错误信息如下：\n\n> Failed to scrape URL. Status code: 402. Error: Insufficient credits to perform this request. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing or try changing the request limit to a lower value.\n\n这意味着当前的请求因信用额度不足而无法完成。为了获取该网页的正文内容，您可以考虑以下几种解决方案：\n\n1. **升级计划**：访问 [https://firecrawl.dev/pricing](https://firecrawl.dev/pricing) 升级您的计划以获得更多的信用额度。\n2. **降低请求限制**：如果可能，尝试减少请求的限制值。\n3. **稍后重试**：有时候系统资源紧张可能导致暂时的信用额度不足，稍后再试可能会解决问题。\n\n如果您有其他网页需要采集或有其他需求，请随时告诉我！我会尽力帮助您。', source_url=None, keywords='老板效果差', source='组合', time='2025-06-30 15:28:08', platform_name=None, selected=False, sentiment='neutral', views=0, comments=0, images=[]), KeywordDataResponseModel(id=1605, title='AI“下沉”千元手机', content='在尝试采集网页 `http://finance.eastmoney.com/a/202505193408529182.html` 的正文时，遇到了一些问题。具体错误信息如下：\n\n> Failed to scrape URL. Status code: 402. Error: Insufficient credits to perform this request. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing or try changing the request limit to a lower value.\n\n这意味着当前的请求因信用额度不足而无法完成。为了获取该网页的正文内容，您可以考虑以下几种解决方案：\n\n1. **升级计划**：访问 [https://firecrawl.dev/pricing](https://firecrawl.dev/pricing) 升级您的计划以获得更多的信用额度。\n2. **降低请求限制**：如果可能，尝试减少请求的限制值。\n3. **稍后重试**：有时候系统可能会有临时的限制，稍后再试可能会解决问题。\n\n如果您有其他网页需要采集或者需要进一步的帮助，请随时告诉我！', source_url=None, keywords='老板效果差', source='组合', time='2025-06-30 15:27:41', platform_name=None, selected=False, sentiment='neutral', views=0, comments=0, images=[]), KeywordDataResponseModel(id=1604, title='止咳宝片发货量减少致营收下降，特一药业的1亿盒目标何以实现？', content='在尝试采集网页 `http://finance.eastmoney.com/a/202505193408555036.html` 的正文时遇到了问题，具体错误信息为：“Failed to wait for the message endpoint”。\n\n这可能是由于网络连接问题、页面加载超时或其他技术原因导致的。为了确保能够获取到您需要的信息，我们可以尝试以下几种方法：\n\n### 1. **再次尝试采集**\n有时候，简单的重试可以解决临时性的问题。\n\n### 2. **使用不同的采集选项**\n我们可以调整采集参数，例如增加等待时间或选择其他格式进行采集。\n\n### 3. **手动访问网页**\n您可以尝试手动访问该网页，并复制粘贴正文内容。\n\n### 4. **检查网页是否正常访问**\n请确认该网页当前是否可以正常访问，有时网页可能会因为维护或其他原因暂时无法访问。\n\n### 再次尝试采集\n\n我将再次尝试采集该网页的正文内容。\n在尝试采集网页 `http://finance.eastmoney.com/a/202505193408555036.html` 的正文时，遇到了资源限制的问题，具体错误信息为：“Failed to scrape URL. Status code: 402. Error: Insufficient credits to perform this request. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing or try changing the request limit to a lower value。”\n\n### 解决方案\n\n#### 1. **手动访问网页**\n鉴于当前自动采集遇到限制，最直接的方法是手动访问该网页并复制正文内容。\n\n#### 2. **调整采集策略**\n如果后续有更多采集需求，可以考虑以下策略：\n- **降低请求频率**：避免短时间内频繁请求。\n- **优化采集计划**：合理安排采集任务，避开高峰时段。\n- **升级服务计划**：如果经常需要大量采集，可以考虑升级服务计划以获得更多的资源额度。\n\n### 手动访问步骤\n\n1. **打开浏览器**：使用您常用的浏览器。\n2. **访问网页**：在地址栏输入 `http://finance.eastmoney.com/a/202505193408555036.html` 并回车。\n3. **定位正文**：找到页面中的文章正文部分。\n4. **复制内容**：选中正文内容并复制。\n\n### 提供帮助\n\n如果您需要进一步的帮助，例如指导如何手动复制内容或有其他相关问题，请随时告知。同时，如果有其他网页需要采集，也可以提供链接，我们将尽力协助。', source_url=None, keywords='老板效果差', source='组合', time='2025-06-30 15:27:12', platform_name=None, selected=False, sentiment='neutral', views=0, comments=0, images=[]), KeywordDataResponseModel(id=1603, title='*ST兰黄首次回购股票约26万股；华致酒行称未来门店发展将主要依托三种业态模式丨酒业早参', content='在尝试采集网页 `http://finance.eastmoney.com/a/202505143403841159.html` 的正文时遇到了问题，具体错误信息为 "Failed to wait for the message endpoint"。这可能是因为页面加载或网络连接的问题。\n\n为了确保能够获取到正确的正文内容，我们可以尝试以下几种方法：\n\n### 方法一：直接访问网页\n你可以直接访问该网页并复制正文内容。\n\n### 方法二：使用不同的工具或方法进行采集\n如果需要我继续尝试其他方式来采集该网页的正文，请告知我，我会尽力提供帮助。\n\n### 方法三：手动输入网址\n如果你有该网页的具体内容或截图，也可以直接提供给我，我可以帮你整理和分析。\n\n请根据上述建议选择合适的方法，或者告诉我是否有其他特定的需求。', source_url=None, keywords='老板效果差', source='组合', time='2025-06-30 15:26:12', platform_name=None, selected=False, sentiment='neutral', views=0, comments=0, images=[]), KeywordDataResponseModel(id=1602, title='“网红儿童”成牟利工具？专家呼吁尊重未成年人真实意愿', content='在尝试采集网页 `http://finance.eastmoney.com/a/202505313419495722.html` 的正文内容时，遇到了一些问题。具体错误信息如下：\n\n> Failed to scrape URL. Status code: 402. Error: Insufficient credits to perform this request. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing or try changing the request limit to a lower value.\n\n这意味着当前的请求因信用额度不足而无法完成。为了获取该网页的正文内容，您可以考虑以下几种解决方案：\n\n1. **升级计划**：访问 [https://firecrawl.dev/pricing](https://firecrawl.dev/pricing) 升级您的计划以获得更多的信用额度。\n2. **降低请求限制**：如果可能，尝试减少每次请求的数据量或调整请求参数。\n3. **稍后重试**：有时候系统资源紧张可能导致暂时的信用额度不足，稍后再试可能会解决问题。\n\n如果您有其他网页需要采集或者需要进一步的帮助，请随时告知！', source_url=None, keywords='老板效果差', source='组合', time='2025-06-30 15:25:49', platform_name=None, selected=False, sentiment='neutral', views=0, comments=0, images=[]), KeywordDataResponseModel(id=1601, title='叮咚买菜一季度财报：GMV 59.6亿元 同比增长7.9%', content='在尝试采集网页 `http://finance.eastmoney.com/a/202505163407082510.html` 的正文时遇到了问题。具体错误信息如下：\n\n> Failed to scrape URL. Status code: 402. Error: Insufficient credits to perform this request. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing or try changing the request limit to a lower value.\n\n这意味着当前的请求因信用额度不足而无法完成。为了获取该网页的正文内容，您可以考虑以下几种解决方案：\n\n1. **升级计划**：访问 [https://firecrawl.dev/pricing](https://firecrawl.dev/pricing) 升级您的计划以获得更多的信用额度。\n2. **调整请求限制**：如果可能，尝试降低请求的限制值。\n3. **稍后重试**：有时候系统资源紧张可能导致暂时的信用额度不足，稍后再试可能会解决问题。\n\n如果您有其他网页需要采集或者需要进一步的帮助，请随时告知！', source_url=None, keywords='老板效果差', source='组合', time='2025-06-30 15:25:21', platform_name=None, selected=False, sentiment='neutral', views=0, comments=0, images=[]), KeywordDataResponseModel(id=1600, title='发起内部<em>效</em>率革命 汽车巨头掀起整合浪潮', content='在尝试采集网页 `http://finance.eastmoney.com/a/202505243413447388.html` 的正文时，遇到了一些问题。具体错误信息如下：\n\n> Failed to scrape URL. Status code: 402. Error: Insufficient credits to perform this request. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing or try changing the request limit to a lower value.\n\n这意味着当前的请求因信用额度不足而无法完成。为了获取该网页的正文内容，您可以考虑以下几种解决方案：\n\n1. **升级计划**：访问 [https://firecrawl.dev/pricing](https://firecrawl.dev/pricing) 升级您的计划以获得更多的信用额度。\n2. **减少请求限制**：如果可能，尝试降低单次请求的限制值。\n3. **稍后重试**：有时候系统资源紧张可能导致暂时的信用不足，稍后再试可能会解决问题。\n\n如果您有其他网页需要采集或者需要进一步的帮助，请随时告诉我！', source_url=None, keywords='老板效果差', source='组合', time='2025-06-30 15:25:04', platform_name=None, selected=False, sentiment='neutral', views=0, comments=0, images=[]), KeywordDataResponseModel(id=1599, title='河南<em>老板</em>开火锅店年入23亿', content='在尝试采集网页 `http://finance.eastmoney.com/a/202506203436154351.html` 的正文时遇到了问题。具体错误信息如下：\n\n> Failed to scrape URL. Status code: 402. Error: Insufficient credits to perform this request. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing or try changing the request limit to a lower value.\n\n这意味着当前的请求因信用额度不足而无法完成。为了获取该网页的正文内容，您可以考虑以下几种解决方案：\n\n1. **升级计划**：访问 [https://firecrawl.dev/pricing](https://firecrawl.dev/pricing) 升级您的计划以获得更多的信用额度。\n2. **减少请求限制**：如果可能，尝试降低单次请求的限制值。\n3. **稍后重试**：有时候系统资源紧张可能导致暂时的信用不足，稍后再试可能会解决问题。\n\n如果您有其他网页需要采集或有进一步的需求，请随时告知！', source_url=None, keywords='老板效果差', source='组合', time='2025-06-30 15:24:29', platform_name=None, selected=False, sentiment='neutral', views=0, comments=0, images=[]), KeywordDataResponseModel(id=1598, title='年轻人涌入亚朵们 五星级酒店“打骨折”拍卖 煤<em>老板</em>们也不接盘了', content='在尝试采集网页 `http://finance.eastmoney.com/a/202506273441808143.html` 的正文时遇到了问题。具体错误信息如下：\n\n> Failed to scrape URL. Status code: 402. Error: Insufficient credits to perform this request. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing or try changing the request limit to a lower value.\n\n这表示当前的请求因信用额度不足而无法完成。为了获取该网页的正文内容，您可以考虑以下几种解决方案：\n\n1. **升级计划**：访问 [https://firecrawl.dev/pricing](https://firecrawl.dev/pricing) 升级您的计划以获得更多的信用额度。\n2. **减少请求限制**：如果可能，尝试降低请求的限制值。\n3. **稍后重试**：有时候系统资源紧张可能导致暂时的信用额度不足，稍后再试可能会解决问题。\n\n如果您有其他网页需要采集或有其他需求，请随时告知！', source_url=None, keywords='老板效果差', source='组合', time='2025-06-30 15:24:06', platform_name=None, selected=False, sentiment='neutral', views=0, comments=0, images=[]), KeywordDataResponseModel(id=1597, title='聊城<em>老板</em>卖猫狗粮 暴赚230亿身家', content='在尝试采集网页 `http://finance.eastmoney.com/a/202506263441590083.html` 的正文时遇到了一些问题，具体错误信息为“Failed to wait for the message endpoint”。这可能是因为页面加载或响应出现了延迟。\n\n为了确保能够成功获取到正文内容，我们可以尝试以下几种方法：\n\n### 方法一：增加等待时间\n有时候，网页内容需要更多时间来加载。我们可以通过增加等待时间来解决这个问题。\n\n### 方法二：直接抓取 HTML 内容\n如果上述方法仍然无法解决问题，我们可以尝试直接抓取整个 HTML 页面，然后手动提取正文部分。\n\n### 方法三：检查网络状况\n确保网络连接稳定，有时候网络问题也会导致页面加载失败。\n\n### 尝试方案\n\n现在，我将尝试直接抓取该网页的 HTML 内容，然后再从中提取正文部分。\n在第二次尝试中，我们增加了等待时间和超时时间，但仍然遇到了“Failed to wait for the message endpoint”的问题。这表明页面加载或响应的问题依然存在。\n\n### 备选方案\n\n#### 方案一：使用不同的抓取工具\n我们可以尝试使用其他网页抓取工具或库来获取该网页的内容。\n\n#### 方案二：手动访问网页\n您可以手动访问该网页，并复制正文内容。这是最直接的方法，可以确保获得所需的信息。\n\n#### 方案三：检查网页是否存在\n请确认该网页是否仍然存在且可访问。有时候，网页可能会因为各种原因（如服务器问题、链接失效等）而无法正常加载。\n\n### 当前建议\n\n鉴于当前情况，我建议您手动访问该网页并复制正文内容。这是目前最可靠的方法来获取所需信息。\n\n如果您同意，请提供手动复制的正文内容，我可以帮助您进一步处理或分析。\n\n如果需要进一步的技术支持或其他帮助，请随时告知！', source_url=None, keywords='老板效果差', source='组合', time='2025-06-30 15:21:48', platform_name=None, selected=False, sentiment='neutral', views=0, comments=0, images=[])] page_num=1 page_size=10 total=1594 has_next=True
2025-07-03 11:29:39.854 | 89e67a8fa26d449c87b7a698949387c3 | INFO     | module_admin.controller.dict_controller:query_system_dict_type_data:145 - 获取成功
2025-07-03 11:29:39.857 | f2eb9de62f774a36a95fc2279c2dd073 | INFO     | module_admin.controller.dict_controller:query_system_dict_type_data:145 - 获取成功
2025-07-03 11:29:40.258 | 3a8f9731773b4903942901082bfad48e | INFO     | module_admin.controller.menu_controller:get_system_menu_list:56 - 获取成功
2025-07-03 11:29:46.417 | d91e5f1e7650436fa9157bf8a6b1cdd3 | INFO     | module_admin.controller.menu_controller:get_system_menu_list:56 - 获取成功
