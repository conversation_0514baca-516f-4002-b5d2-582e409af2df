{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue?vue&type=template&id=040a21b8&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue", "mtime": 1751520809838}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750933729645}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750933731210}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750933730285}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}