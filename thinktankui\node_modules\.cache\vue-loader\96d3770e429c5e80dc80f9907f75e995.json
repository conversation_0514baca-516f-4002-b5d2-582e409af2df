{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue?vue&type=template&id=040a21b8&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue", "mtime": 1751517278069}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750933729645}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750933731210}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750933730285}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}