<template>
  <div class="report-container">
    <!-- 报表头部 -->
    <div class="report-header">
      <div class="report-title">
        <h1>信息汇总分析报表</h1>
        <div class="report-meta">
          <span class="report-date">生成时间：{{ reportDate }}</span>
          <span class="report-range" v-if="dateRange">数据范围：{{ dateRange }}</span>
        </div>
      </div>
      <div class="report-logo">
        <img src="/logo.png" alt="Logo" class="logo-img" />
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="report-section">
      <h2 class="section-title">数据概览</h2>
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-number">{{ totalCount }}</div>
          <div class="stat-label">总信息数</div>
        </div>
        <div class="stat-card positive">
          <div class="stat-number">{{ positiveCount }}</div>
          <div class="stat-label">正面信息</div>
        </div>
        <div class="stat-card neutral">
          <div class="stat-number">{{ neutralCount }}</div>
          <div class="stat-label">中性信息</div>
        </div>
        <div class="stat-card negative">
          <div class="stat-number">{{ negativeCount }}</div>
          <div class="stat-label">负面信息</div>
        </div>
      </div>
    </div>

    <!-- 图表分析 -->
    <div class="report-section">
      <h2 class="section-title">数据分析</h2>
      <div class="charts-grid">
        <div class="chart-container">
          <h3 class="chart-title">情感倾向分布</h3>
          <div ref="sentimentChart" class="chart"></div>
        </div>
        <div class="chart-container">
          <h3 class="chart-title">来源分布</h3>
          <div ref="sourceChart" class="chart"></div>
        </div>
      </div>
    </div>

    <!-- 详细数据列表 -->
    <div class="report-section">
      <h2 class="section-title">详细信息列表</h2>
      <div class="data-table">
        <table class="report-table">
          <thead>
            <tr>
              <th>序号</th>
              <th>标题</th>
              <th>来源</th>
              <th>时间</th>
              <th>情感倾向</th>
              <th>浏览量</th>
              <th>评论数</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in reportData" :key="index">
              <td>{{ index + 1 }}</td>
              <td class="title-cell" v-html="item.title"></td>
              <td>{{ item.source }}</td>
              <td>{{ item.time }}</td>
              <td>
                <span :class="'sentiment-tag sentiment-' + item.sentiment">
                  {{ getSentimentText(item.sentiment) }}
                </span>
              </td>
              <td>{{ item.views || 0 }}</td>
              <td>{{ item.comments || 0 }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 报表尾部 -->
    <div class="report-footer">
      <div class="footer-info">
        <p>本报表由智库系统自动生成</p>
        <p>生成时间：{{ reportDate }}</p>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'ReportTemplate',
  props: {
    reportData: {
      type: Array,
      default: () => []
    },
    dateRange: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      reportDate: new Date().toLocaleString('zh-CN')
    }
  },
  computed: {
    totalCount() {
      return this.reportData.length
    },
    positiveCount() {
      return this.reportData.filter(item => item.sentiment === 'positive').length
    },
    neutralCount() {
      return this.reportData.filter(item => item.sentiment === 'neutral').length
    },
    negativeCount() {
      return this.reportData.filter(item => item.sentiment === 'negative').length
    },
    sentimentData() {
      return [
        { name: '正面', value: this.positiveCount, itemStyle: { color: '#67C23A' } },
        { name: '中性', value: this.neutralCount, itemStyle: { color: '#E6A23C' } },
        { name: '负面', value: this.negativeCount, itemStyle: { color: '#F56C6C' } }
      ]
    },
    sourceData() {
      const sourceMap = {}
      this.reportData.forEach(item => {
        const source = item.source || '未知来源'
        sourceMap[source] = (sourceMap[source] || 0) + 1
      })
      return Object.entries(sourceMap).map(([name, value]) => ({ name, value }))
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initCharts()
    })
  },
  methods: {
    getSentimentText(sentiment) {
      const texts = {
        positive: '正面',
        neutral: '中性',
        negative: '负面'
      }
      return texts[sentiment] || '未知'
    },
    initCharts() {
      this.initSentimentChart()
      this.initSourceChart()
    },
    initSentimentChart() {
      if (!this.$refs.sentimentChart) return
      
      const chart = echarts.init(this.$refs.sentimentChart)
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 10,
          data: ['正面', '中性', '负面']
        },
        series: [
          {
            name: '情感倾向',
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['60%', '50%'],
            data: this.sentimentData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
      chart.setOption(option)
    },
    initSourceChart() {
      if (!this.$refs.sourceChart) return
      
      const chart = echarts.init(this.$refs.sourceChart)
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          boundaryGap: [0, 0.01]
        },
        yAxis: {
          type: 'category',
          data: this.sourceData.map(item => item.name)
        },
        series: [
          {
            name: '数量',
            type: 'bar',
            data: this.sourceData.map(item => item.value),
            itemStyle: {
              color: '#409EFF'
            }
          }
        ]
      }
      chart.setOption(option)
    }
  }
}
</script>

<style lang="scss" scoped>
.report-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background: white;
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  line-height: 1.6;
  color: #333;
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #409EFF;
  
  .report-title {
    h1 {
      margin: 0;
      color: #409EFF;
      font-size: 28px;
      font-weight: bold;
    }
    
    .report-meta {
      margin-top: 10px;
      font-size: 14px;
      color: #666;
      
      span {
        margin-right: 20px;
      }
    }
  }
  
  .report-logo {
    .logo-img {
      height: 60px;
      width: auto;
    }
  }
}

.report-section {
  margin-bottom: 40px;
  
  .section-title {
    font-size: 20px;
    color: #409EFF;
    margin-bottom: 20px;
    padding-left: 10px;
    border-left: 4px solid #409EFF;
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
  
  .stat-card {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    border-left: 4px solid #409EFF;
    
    &.positive {
      border-left-color: #67C23A;
      background: #f0f9ff;
    }
    
    &.neutral {
      border-left-color: #E6A23C;
      background: #fefce8;
    }
    
    &.negative {
      border-left-color: #F56C6C;
      background: #fef2f2;
    }
    
    .stat-number {
      font-size: 32px;
      font-weight: bold;
      color: #409EFF;
      margin-bottom: 5px;
    }
    
    .stat-label {
      font-size: 14px;
      color: #666;
    }
  }
}

.charts-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  
  .chart-container {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    
    .chart-title {
      font-size: 16px;
      color: #333;
      margin-bottom: 15px;
      text-align: center;
    }
    
    .chart {
      height: 300px;
      width: 100%;
    }
  }
}

.data-table {
  overflow-x: auto;
  
  .report-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
    
    th, td {
      padding: 12px 8px;
      text-align: left;
      border-bottom: 1px solid #eee;
    }
    
    th {
      background: #f8f9fa;
      font-weight: bold;
      color: #333;
      border-bottom: 2px solid #409EFF;
    }
    
    tr:hover {
      background: #f5f5f5;
    }
    
    .title-cell {
      max-width: 300px;
      word-break: break-word;
    }
    
    .sentiment-tag {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: bold;
      
      &.sentiment-positive {
        background: #f0f9ff;
        color: #67C23A;
        border: 1px solid #67C23A;
      }
      
      &.sentiment-neutral {
        background: #fefce8;
        color: #E6A23C;
        border: 1px solid #E6A23C;
      }
      
      &.sentiment-negative {
        background: #fef2f2;
        color: #F56C6C;
        border: 1px solid #F56C6C;
      }
    }
  }
}

.report-footer {
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #eee;
  text-align: center;
  color: #666;
  font-size: 12px;
}

@media print {
  .report-container {
    padding: 0;
    box-shadow: none;
  }
  
  .charts-grid {
    grid-template-columns: 1fr;
  }
  
  .chart {
    height: 250px !important;
  }
}
</style>
