from contextlib import asynccontextmanager
from fastapi import Fast<PERSON><PERSON>
from config.env import AppConfig
from config.get_db import init_create_table
from config.get_redis import RedisUtil
from config.get_scheduler import SchedulerUtil
from exceptions.handle import handle_exception
from middlewares.handle import handle_middleware
from module_admin.controller.cache_controller import cacheController
from module_admin.controller.captcha_controller import captchaController
from module_admin.controller.common_controller import commonController
from module_admin.controller.config_controller import configController
from module_admin.controller.dept_controller import deptController
from module_admin.controller.dict_controller import dictController
from module_admin.controller.emotion_analysis_controller import emotionAnalysis<PERSON>ontroller, internalEmotionAnalysisController
from module_admin.controller.info_summary_controller import infoSummaryController
from module_admin.controller.keyword_data_controller import keywordDataController, publicKeywordDataController
from module_admin.controller.log_controller import logController
from module_admin.controller.login_controller import login<PERSON>ontroller
from module_admin.controller.job_controller import jobController
from module_admin.controller.menu_controller import menuController
from module_admin.controller.notice_controller import noticeController
from module_admin.controller.online_controller import onlineController
from module_admin.controller.post_controler import postController
from module_admin.controller.role_controller import roleController
from module_admin.controller.server_controller import serverController
from module_admin.controller.user_controller import userController
from module_admin.controller.report_controller import reportController
from module_generator.controller.gen_controller import genController
from module_warning.controller.warning_record_controller import warningRecordController
from module_warning.controller.warning_scheme_controller import warningSchemeController
from module_warning.controller.warning_settings_controller import warningSettingsController
from module_warning.controller.warning_frontend_controller import warningFrontendController
from sub_applications.handle import handle_sub_applications
from utils.common_util import worship
from utils.log_util import logger

# 导入API映射控制器和系统路由别名
from module_admin.controller.api_mapping_controller import (
    spreadAnalysisController,
    metaSearchController,
    hotEventsController,
    eventAnalysisController,
    sourceMonitoringController
)
from module_admin.controller.report_controller import systemSchemeController
from module_admin.controller.info_summary_controller import systemInfoSummaryController

# 导入API路由映射控制器
from module_admin.controller.api_mapping_controller import (
    spreadAnalysisController,
    metaSearchController,
    hotEventsController,
    eventAnalysisController,
    sourceMonitoringController
)


# 生命周期事件
@asynccontextmanager
async def lifespan(app: FastAPI):
    logger.info(f'{AppConfig.app_name}开始启动')
    worship()
    await init_create_table()
    app.state.redis = await RedisUtil.create_redis_pool()
    await RedisUtil.init_sys_dict(app.state.redis)
    await RedisUtil.init_sys_config(app.state.redis)
    await SchedulerUtil.init_system_scheduler()
    logger.info(f'{AppConfig.app_name}启动成功')
    yield
    await RedisUtil.close_redis_pool(app)
    await SchedulerUtil.close_system_scheduler()


# 初始化FastAPI对象
app = FastAPI(
    title=AppConfig.app_name,
    description=f'{AppConfig.app_name}接口文档',
    version=AppConfig.app_version,
    lifespan=lifespan,
)

# 挂载子应用
handle_sub_applications(app)
# 加载中间件处理方法
handle_middleware(app)
# 加载全局异常处理方法
handle_exception(app)


# 加载路由列表
controller_list = [
    {'router': loginController, 'tags': ['登录模块']},
    {'router': captchaController, 'tags': ['验证码模块']},
    {'router': userController, 'tags': ['系统管理-用户管理']},
    {'router': roleController, 'tags': ['系统管理-角色管理']},
    {'router': menuController, 'tags': ['系统管理-菜单管理']},
    {'router': deptController, 'tags': ['系统管理-部门管理']},
    {'router': postController, 'tags': ['系统管理-岗位管理']},
    {'router': dictController, 'tags': ['系统管理-字典管理']},
    {'router': configController, 'tags': ['系统管理-参数管理']},
    {'router': noticeController, 'tags': ['系统管理-通知公告管理']},
    {'router': logController, 'tags': ['系统管理-日志管理']},
    {'router': onlineController, 'tags': ['系统监控-在线用户']},
    {'router': jobController, 'tags': ['系统监控-定时任务']},
    {'router': serverController, 'tags': ['系统监控-菜单管理']},
    {'router': cacheController, 'tags': ['系统监控-缓存监控']},
    {'router': commonController, 'tags': ['通用模块']},
    {'router': genController, 'tags': ['代码生成']},
    {'router': reportController, 'tags': ['报告中心']},
    {'router': emotionAnalysisController, 'tags': ['情感分析-公开接口']},
    {'router': internalEmotionAnalysisController, 'tags': ['情感分析-内部接口']},
    {'router': infoSummaryController, 'tags': ['信息汇总管理']},
    {'router': keywordDataController, 'tags': ['关键词数据管理']},
    {'router': publicKeywordDataController, 'tags': ['关键词数据公开接口']},
    {'router': warningRecordController, 'tags': ['预警管理-预警记录']},
    {'router': warningSchemeController, 'tags': ['预警管理-预警方案']},
    {'router': warningSettingsController, 'tags': ['预警管理-预警设置']},
    {'router': warningFrontendController, 'tags': ['预警管理-前端接口']},
    # API映射路由
    {'router': spreadAnalysisController, 'tags': ['API映射-传播分析']},
    {'router': metaSearchController, 'tags': ['API映射-元搜索']},
    {'router': hotEventsController, 'tags': ['API映射-热点事件']},
    {'router': eventAnalysisController, 'tags': ['API映射-事件分析']},
    {'router': sourceMonitoringController, 'tags': ['API映射-信源监测']},
    # 系统路由别名
    {'router': systemSchemeController, 'tags': ['系统别名-方案管理']},
    {'router': systemInfoSummaryController, 'tags': ['系统别名-信息汇总']},
]

for controller in controller_list:
    app.include_router(router=controller.get('router'), tags=controller.get('tags'))
