{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue", "mtime": 1751520491987}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\babel.config.js", "mtime": 1750933247176}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750933729645}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750933730285}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBuYW1lOiAnT3BpbmlvbkFuYWx5c2lzJywKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy8g6aG16Z2i5pWw5o2u5bCG5Zyo6L+Z6YeM5a6a5LmJCiAgICB9OwogIH0sCiAgbW91bnRlZDogZnVuY3Rpb24gbW91bnRlZCgpIHsKICAgIC8vIOmhtemdouWIneWni+WMlumAu+i+kQogICAgY29uc29sZS5sb2coJ+iIhuaDheWIhuaekOmhtemdouW3suWKoOi9vScpOwogIH0sCiAgbWV0aG9kczogewogICAgLy8g6aG16Z2i5pa55rOV5bCG5Zyo6L+Z6YeM5a6a5LmJCiAgfQp9Ow=="}, {"version": 3, "names": ["name", "data", "mounted", "console", "log", "methods"], "sources": ["src/views/opinion-analysis/index.vue"], "sourcesContent": ["<template>\n  <div class=\"opinion-analysis\">\n    <!-- 3D头像 -->\n    <div class=\"avatar-container\">\n      <div class=\"avatar-3d\">\n        <div class=\"avatar-face\">😊</div>\n        <div class=\"avatar-greeting\">hi</div>\n      </div>\n    </div>\n\n    <!-- 主要聊天界面 -->\n    <div class=\"chat-container\">\n      <!-- 问候语 -->\n      <div class=\"greeting-section\">\n        <h1 class=\"greeting-text\">你好，{{ userName }}</h1>\n      </div>\n\n      <!-- 聊天输入区域 -->\n      <div class=\"chat-input-section\">\n        <div class=\"chat-input-container\">\n          <div class=\"input-header\">\n            <span class=\"time-info\">{{ timeInfo }}</span>\n            <span class=\"status-info\">{{ statusInfo }}</span>\n          </div>\n\n          <div class=\"input-wrapper\">\n            <el-input\n              v-model=\"inputMessage\"\n              type=\"textarea\"\n              :rows=\"3\"\n              :placeholder=\"placeholderText\"\n              class=\"chat-input\"\n              @keyup.enter.ctrl=\"sendMessage\"\n            />\n          </div>\n\n          <!-- 功能按钮区域 -->\n          <div class=\"function-buttons\">\n            <div class=\"left-buttons\">\n              <el-button\n                type=\"text\"\n                icon=\"el-icon-paperclip\"\n                class=\"function-btn\"\n                @click=\"handleAttachment\">\n                附件\n              </el-button>\n              <el-button\n                type=\"text\"\n                icon=\"el-icon-s-grid\"\n                class=\"function-btn\"\n                @click=\"handleExtension\">\n                扩展\n              </el-button>\n              <el-button\n                type=\"text\"\n                icon=\"el-icon-magic-stick\"\n                class=\"function-btn\"\n                @click=\"handleAuto\">\n                自动\n              </el-button>\n            </div>\n\n            <div class=\"right-buttons\">\n              <el-button\n                type=\"primary\"\n                icon=\"el-icon-s-promotion\"\n                class=\"send-btn\"\n                @click=\"sendMessage\"\n                :disabled=\"!inputMessage.trim()\">\n                发送\n              </el-button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'OpinionAnalysis',\n  data() {\n    return {\n      // 页面数据将在这里定义\n    }\n  },\n  mounted() {\n    // 页面初始化逻辑\n    console.log('舆情分析页面已加载')\n  },\n  methods: {\n    // 页面方法将在这里定义\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.opinion-analysis {\n  padding: 0;\n  background-color: #f5f5f5;\n  min-height: 100vh;\n}\n\n.page-header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 40px 24px;\n  margin-bottom: 24px;\n\n  .header-content {\n    max-width: 1200px;\n    margin: 0 auto;\n\n    .page-title {\n      font-size: 32px;\n      font-weight: 600;\n      margin: 0 0 12px 0;\n      display: flex;\n      align-items: center;\n\n      i {\n        margin-right: 12px;\n        font-size: 36px;\n      }\n    }\n\n    .page-description {\n      font-size: 16px;\n      opacity: 0.9;\n      margin: 0;\n    }\n  }\n}\n\n.main-content {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 24px;\n}\n\n.feature-cards {\n  margin-bottom: 32px;\n\n  .feature-card {\n    background: white;\n    border-radius: 12px;\n    padding: 32px 24px;\n    text-align: center;\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n    transition: all 0.3s ease;\n    height: 200px;\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n\n    &:hover {\n      transform: translateY(-4px);\n      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\n    }\n\n    .card-icon {\n      margin-bottom: 16px;\n\n      i {\n        font-size: 48px;\n        color: #667eea;\n      }\n    }\n\n    .card-content {\n      h3 {\n        font-size: 18px;\n        font-weight: 600;\n        margin: 0 0 8px 0;\n        color: #333;\n      }\n\n      p {\n        font-size: 14px;\n        color: #666;\n        margin: 0;\n        line-height: 1.5;\n      }\n    }\n  }\n}\n\n.placeholder-content {\n  .content-card {\n    border-radius: 12px;\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n\n    .card-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      font-weight: 600;\n      font-size: 16px;\n    }\n  }\n}\n\n.empty-state {\n  text-align: center;\n  padding: 60px 20px;\n\n  .empty-icon {\n    margin-bottom: 24px;\n\n    i {\n      font-size: 80px;\n      color: #d9d9d9;\n    }\n  }\n\n  h3 {\n    font-size: 20px;\n    color: #333;\n    margin: 0 0 12px 0;\n    font-weight: 600;\n  }\n\n  p {\n    font-size: 16px;\n    color: #666;\n    margin: 0 0 32px 0;\n  }\n\n  .coming-features {\n    display: flex;\n    justify-content: center;\n    flex-wrap: wrap;\n    gap: 12px;\n\n    .feature-tag {\n      font-size: 14px;\n      padding: 8px 16px;\n      border-radius: 20px;\n    }\n  }\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  .page-header {\n    padding: 24px 16px;\n\n    .page-title {\n      font-size: 24px;\n\n      i {\n        font-size: 28px;\n      }\n    }\n\n    .page-description {\n      font-size: 14px;\n    }\n  }\n\n  .main-content {\n    padding: 0 16px;\n  }\n\n  .feature-cards {\n    .el-col {\n      margin-bottom: 16px;\n    }\n\n    .feature-card {\n      height: auto;\n      padding: 24px 16px;\n    }\n  }\n}\n</style>\n"], "mappings": ";;;;;;iCAgFA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;IAAA,CACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACAC,OAAA,CAAAC,GAAA;EACA;EACAC,OAAA;IACA;EAAA;AAEA", "ignoreList": []}]}