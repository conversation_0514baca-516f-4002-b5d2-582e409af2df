{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue?vue&type=style&index=0&id=040a21b8&lang=scss&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue", "mtime": 1751520491987}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750933728705}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750933731152}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750933729640}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750933728031}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750933730285}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAkGA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/opinion-analysis", "sourcesContent": ["<template>\n  <div class=\"opinion-analysis\">\n    <!-- 3D头像 -->\n    <div class=\"avatar-container\">\n      <div class=\"avatar-3d\">\n        <div class=\"avatar-face\">😊</div>\n        <div class=\"avatar-greeting\">hi</div>\n      </div>\n    </div>\n\n    <!-- 主要聊天界面 -->\n    <div class=\"chat-container\">\n      <!-- 问候语 -->\n      <div class=\"greeting-section\">\n        <h1 class=\"greeting-text\">你好，{{ userName }}</h1>\n      </div>\n\n      <!-- 聊天输入区域 -->\n      <div class=\"chat-input-section\">\n        <div class=\"chat-input-container\">\n          <div class=\"input-header\">\n            <span class=\"time-info\">{{ timeInfo }}</span>\n            <span class=\"status-info\">{{ statusInfo }}</span>\n          </div>\n\n          <div class=\"input-wrapper\">\n            <el-input\n              v-model=\"inputMessage\"\n              type=\"textarea\"\n              :rows=\"3\"\n              :placeholder=\"placeholderText\"\n              class=\"chat-input\"\n              @keyup.enter.ctrl=\"sendMessage\"\n            />\n          </div>\n\n          <!-- 功能按钮区域 -->\n          <div class=\"function-buttons\">\n            <div class=\"left-buttons\">\n              <el-button\n                type=\"text\"\n                icon=\"el-icon-paperclip\"\n                class=\"function-btn\"\n                @click=\"handleAttachment\">\n                附件\n              </el-button>\n              <el-button\n                type=\"text\"\n                icon=\"el-icon-s-grid\"\n                class=\"function-btn\"\n                @click=\"handleExtension\">\n                扩展\n              </el-button>\n              <el-button\n                type=\"text\"\n                icon=\"el-icon-magic-stick\"\n                class=\"function-btn\"\n                @click=\"handleAuto\">\n                自动\n              </el-button>\n            </div>\n\n            <div class=\"right-buttons\">\n              <el-button\n                type=\"primary\"\n                icon=\"el-icon-s-promotion\"\n                class=\"send-btn\"\n                @click=\"sendMessage\"\n                :disabled=\"!inputMessage.trim()\">\n                发送\n              </el-button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'OpinionAnalysis',\n  data() {\n    return {\n      // 页面数据将在这里定义\n    }\n  },\n  mounted() {\n    // 页面初始化逻辑\n    console.log('舆情分析页面已加载')\n  },\n  methods: {\n    // 页面方法将在这里定义\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.opinion-analysis {\n  padding: 0;\n  background-color: #f5f5f5;\n  min-height: 100vh;\n}\n\n.page-header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 40px 24px;\n  margin-bottom: 24px;\n\n  .header-content {\n    max-width: 1200px;\n    margin: 0 auto;\n\n    .page-title {\n      font-size: 32px;\n      font-weight: 600;\n      margin: 0 0 12px 0;\n      display: flex;\n      align-items: center;\n\n      i {\n        margin-right: 12px;\n        font-size: 36px;\n      }\n    }\n\n    .page-description {\n      font-size: 16px;\n      opacity: 0.9;\n      margin: 0;\n    }\n  }\n}\n\n.main-content {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 24px;\n}\n\n.feature-cards {\n  margin-bottom: 32px;\n\n  .feature-card {\n    background: white;\n    border-radius: 12px;\n    padding: 32px 24px;\n    text-align: center;\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n    transition: all 0.3s ease;\n    height: 200px;\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n\n    &:hover {\n      transform: translateY(-4px);\n      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\n    }\n\n    .card-icon {\n      margin-bottom: 16px;\n\n      i {\n        font-size: 48px;\n        color: #667eea;\n      }\n    }\n\n    .card-content {\n      h3 {\n        font-size: 18px;\n        font-weight: 600;\n        margin: 0 0 8px 0;\n        color: #333;\n      }\n\n      p {\n        font-size: 14px;\n        color: #666;\n        margin: 0;\n        line-height: 1.5;\n      }\n    }\n  }\n}\n\n.placeholder-content {\n  .content-card {\n    border-radius: 12px;\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n\n    .card-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      font-weight: 600;\n      font-size: 16px;\n    }\n  }\n}\n\n.empty-state {\n  text-align: center;\n  padding: 60px 20px;\n\n  .empty-icon {\n    margin-bottom: 24px;\n\n    i {\n      font-size: 80px;\n      color: #d9d9d9;\n    }\n  }\n\n  h3 {\n    font-size: 20px;\n    color: #333;\n    margin: 0 0 12px 0;\n    font-weight: 600;\n  }\n\n  p {\n    font-size: 16px;\n    color: #666;\n    margin: 0 0 32px 0;\n  }\n\n  .coming-features {\n    display: flex;\n    justify-content: center;\n    flex-wrap: wrap;\n    gap: 12px;\n\n    .feature-tag {\n      font-size: 14px;\n      padding: 8px 16px;\n      border-radius: 20px;\n    }\n  }\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  .page-header {\n    padding: 24px 16px;\n\n    .page-title {\n      font-size: 24px;\n\n      i {\n        font-size: 28px;\n      }\n    }\n\n    .page-description {\n      font-size: 14px;\n    }\n  }\n\n  .main-content {\n    padding: 0 16px;\n  }\n\n  .feature-cards {\n    .el-col {\n      margin-bottom: 16px;\n    }\n\n    .feature-card {\n      height: auto;\n      padding: 24px 16px;\n    }\n  }\n}\n</style>\n"]}]}