{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue?vue&type=style&index=0&id=040a21b8&lang=scss&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue", "mtime": 1751520809838}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750933728705}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750933731152}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750933729640}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750933728031}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750933730285}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAmLA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/opinion-analysis", "sourcesContent": ["<template>\n  <div class=\"opinion-analysis\">\n    <!-- 3D头像 -->\n    <div class=\"avatar-container\">\n      <div class=\"avatar-3d\">\n        <div class=\"avatar-face\">😊</div>\n        <div class=\"avatar-greeting\">hi</div>\n      </div>\n    </div>\n\n    <!-- 主要聊天界面 -->\n    <div class=\"chat-container\">\n      <!-- 问候语 -->\n      <div class=\"greeting-section\">\n        <h1 class=\"greeting-text\">你好，{{ userName }}</h1>\n      </div>\n\n      <!-- 聊天输入区域 -->\n      <div class=\"chat-input-section\">\n        <div class=\"chat-input-container\">\n          <div class=\"input-header\">\n            <span class=\"time-info\">{{ timeInfo }}</span>\n            <span class=\"status-info\">{{ statusInfo }}</span>\n          </div>\n\n          <div class=\"input-wrapper\">\n            <el-input\n              v-model=\"inputMessage\"\n              type=\"textarea\"\n              :rows=\"3\"\n              :placeholder=\"placeholderText\"\n              class=\"chat-input\"\n              @keyup.enter.ctrl=\"sendMessage\"\n            />\n          </div>\n\n          <!-- 功能按钮区域 -->\n          <div class=\"function-buttons\">\n            <div class=\"left-buttons\">\n              <el-button\n                type=\"text\"\n                icon=\"el-icon-paperclip\"\n                class=\"function-btn\"\n                @click=\"handleAttachment\">\n                附件\n              </el-button>\n              <el-button\n                type=\"text\"\n                icon=\"el-icon-s-grid\"\n                class=\"function-btn\"\n                @click=\"handleExtension\">\n                扩展\n              </el-button>\n              <el-button\n                type=\"text\"\n                icon=\"el-icon-magic-stick\"\n                class=\"function-btn\"\n                @click=\"handleAuto\">\n                自动\n              </el-button>\n            </div>\n\n            <div class=\"right-buttons\">\n              <el-button\n                type=\"primary\"\n                icon=\"el-icon-s-promotion\"\n                class=\"send-btn\"\n                @click=\"sendMessage\"\n                :disabled=\"!inputMessage.trim()\">\n                发送\n              </el-button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'OpinionAnalysis',\n  data() {\n    return {\n      userName: '',\n      inputMessage: '',\n      currentTime: new Date(),\n      placeholderText: '请输入您的舆情分析需求...'\n    }\n  },\n  computed: {\n    timeInfo() {\n      const now = this.currentTime\n      const today = now.toLocaleDateString('zh-CN', {\n        month: 'long',\n        day: 'numeric',\n        weekday: 'long'\n      })\n      const time = now.toLocaleTimeString('zh-CN', {\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: false\n      })\n      return `${today}，比如：上午 ${time}`\n    },\n    statusInfo() {\n      return '我我：在等待您，比如：舆情AI助手'\n    }\n  },\n  mounted() {\n    // 页面初始化逻辑\n    console.log('舆情分析聊天界面已加载')\n\n    // 获取用户信息\n    this.getUserInfo()\n\n    // 每分钟更新时间\n    this.timeInterval = setInterval(() => {\n      this.currentTime = new Date()\n    }, 60000)\n  },\n  beforeDestroy() {\n    // 清理定时器\n    if (this.timeInterval) {\n      clearInterval(this.timeInterval)\n    }\n  },\n  methods: {\n    // 获取用户信息\n    getUserInfo() {\n      // 从Vuex store中获取用户信息\n      const userInfo = this.$store.state.user.userInfo\n      if (userInfo && userInfo.userName) {\n        this.userName = userInfo.userName\n      } else {\n        // 如果没有用户信息，尝试从localStorage获取\n        const storedUserInfo = localStorage.getItem('userInfo')\n        if (storedUserInfo) {\n          try {\n            const parsedUserInfo = JSON.parse(storedUserInfo)\n            this.userName = parsedUserInfo.userName || '用户'\n          } catch (e) {\n            this.userName = '用户'\n          }\n        } else {\n          this.userName = '用户'\n        }\n      }\n    },\n\n    // 发送消息\n    sendMessage() {\n      if (!this.inputMessage.trim()) return\n\n      console.log('发送消息:', this.inputMessage)\n      // 这里可以添加发送消息的逻辑\n      this.$message.success('消息已发送，舆情分析功能开发中...')\n      this.inputMessage = ''\n    },\n\n    // 处理附件\n    handleAttachment() {\n      this.$message.info('附件功能开发中...')\n    },\n\n    // 处理扩展\n    handleExtension() {\n      this.$message.info('扩展功能开发中...')\n    },\n\n    // 处理自动功能\n    handleAuto() {\n      this.$message.info('自动功能开发中...')\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.opinion-analysis {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  padding: 20px;\n}\n\n// 3D头像样式\n.avatar-container {\n  position: absolute;\n  top: 60px;\n  right: 80px;\n  z-index: 10;\n\n  .avatar-3d {\n    position: relative;\n    width: 80px;\n    height: 80px;\n\n    .avatar-face {\n      width: 80px;\n      height: 80px;\n      background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 32px;\n      box-shadow: 0 8px 32px rgba(255, 154, 158, 0.3);\n      animation: float 3s ease-in-out infinite;\n    }\n\n    .avatar-greeting {\n      position: absolute;\n      top: -10px;\n      left: -20px;\n      background: white;\n      color: #666;\n      padding: 4px 12px;\n      border-radius: 20px;\n      font-size: 14px;\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n      animation: bounce 2s ease-in-out infinite;\n    }\n  }\n}\n\n// 主聊天容器\n.chat-container {\n  width: 100%;\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n// 问候语区域\n.greeting-section {\n  text-align: center;\n  margin-bottom: 60px;\n\n  .greeting-text {\n    font-size: 36px;\n    font-weight: 300;\n    color: #333;\n    margin: 0;\n    letter-spacing: 1px;\n  }\n}\n\n// 聊天输入区域\n.chat-input-section {\n  .chat-input-container {\n    background: white;\n    border-radius: 24px;\n    padding: 24px;\n    box-shadow: 0 8px 40px rgba(0, 0, 0, 0.12);\n    border: 1px solid rgba(0, 0, 0, 0.05);\n\n    .input-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 16px;\n      font-size: 14px;\n      color: #666;\n\n      .time-info {\n        color: #999;\n      }\n\n      .status-info {\n        color: #666;\n      }\n    }\n\n    .input-wrapper {\n      margin-bottom: 16px;\n\n      .chat-input {\n        ::v-deep .el-textarea__inner {\n          border: none;\n          background: #f8f9fa;\n          border-radius: 12px;\n          padding: 16px;\n          font-size: 16px;\n          line-height: 1.5;\n          resize: none;\n          box-shadow: none;\n\n          &:focus {\n            background: #f0f2f5;\n            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);\n          }\n\n          &::placeholder {\n            color: #bbb;\n          }\n        }\n      }\n    }\n\n    .function-buttons {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n\n      .left-buttons {\n        display: flex;\n        gap: 8px;\n\n        .function-btn {\n          color: #666;\n          font-size: 14px;\n          padding: 8px 16px;\n          border-radius: 20px;\n          transition: all 0.3s ease;\n\n          &:hover {\n            background: #f0f2f5;\n            color: #409eff;\n          }\n\n          i {\n            margin-right: 4px;\n          }\n        }\n      }\n\n      .right-buttons {\n        .send-btn {\n          padding: 10px 24px;\n          border-radius: 20px;\n          font-weight: 500;\n          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);\n\n          &:not(:disabled):hover {\n            transform: translateY(-1px);\n            box-shadow: 0 6px 16px rgba(64, 158, 255, 0.4);\n          }\n        }\n      }\n    }\n  }\n}\n\n// 动画效果\n@keyframes float {\n  0%, 100% {\n    transform: translateY(0px);\n  }\n  50% {\n    transform: translateY(-10px);\n  }\n}\n\n@keyframes bounce {\n  0%, 100% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.1);\n  }\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  .opinion-analysis {\n    padding: 16px;\n  }\n\n  .avatar-container {\n    top: 30px;\n    right: 30px;\n\n    .avatar-3d {\n      width: 60px;\n      height: 60px;\n\n      .avatar-face {\n        width: 60px;\n        height: 60px;\n        font-size: 24px;\n      }\n\n      .avatar-greeting {\n        font-size: 12px;\n        padding: 2px 8px;\n      }\n    }\n  }\n\n  .greeting-section {\n    margin-bottom: 40px;\n\n    .greeting-text {\n      font-size: 28px;\n    }\n  }\n\n  .chat-input-section {\n    .chat-input-container {\n      padding: 20px;\n      border-radius: 20px;\n\n      .function-buttons {\n        flex-direction: column;\n        gap: 12px;\n\n        .left-buttons {\n          justify-content: center;\n        }\n\n        .right-buttons {\n          width: 100%;\n\n          .send-btn {\n            width: 100%;\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}