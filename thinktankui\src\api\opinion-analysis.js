import request from '@/utils/request'

// 获取舆情分析数据
export function getOpinionAnalysisData(query) {
  return request({
    url: '/public/opinion-analysis/data',
    method: 'get',
    params: query
  })
}

// 获取情感分析结果
export function getEmotionAnalysisResult(query) {
  return request({
    url: '/public/opinion-analysis/emotion',
    method: 'get',
    params: query
  })
}

// 获取趋势分析数据
export function getTrendAnalysisData(query) {
  return request({
    url: '/public/opinion-analysis/trend',
    method: 'get',
    params: query
  })
}

// 获取地域分析数据
export function getRegionalAnalysisData(query) {
  return request({
    url: '/public/opinion-analysis/regional',
    method: 'get',
    params: query
  })
}

// 创建新的分析任务
export function createAnalysisTask(data) {
  return request({
    url: '/system/opinion-analysis/task',
    method: 'post',
    data: data
  })
}

// 获取分析任务列表
export function getAnalysisTaskList(query) {
  return request({
    url: '/system/opinion-analysis/tasks',
    method: 'get',
    params: query
  })
}

// 删除分析任务
export function deleteAnalysisTask(taskId) {
  return request({
    url: `/system/opinion-analysis/task/${taskId}`,
    method: 'delete'
  })
}

// 获取关键词分析结果
export function getKeywordAnalysis(query) {
  return request({
    url: '/public/opinion-analysis/keywords',
    method: 'get',
    params: query
  })
}

// 获取影响力评估数据
export function getInfluenceAssessment(query) {
  return request({
    url: '/public/opinion-analysis/influence',
    method: 'get',
    params: query
  })
}

// 获取传播路径分析
export function getSpreadPathAnalysis(query) {
  return request({
    url: '/public/opinion-analysis/spread-path',
    method: 'get',
    params: query
  })
}
