import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * roles: ['admin', 'common']       // 访问路由的角色权限
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * meta : {
    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */

// 公共路由
export const constantRoutes = [
  // 系统路由
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect')
      }
    ]
  },
  {
    path: '/login',
    component: () => import('@/views/login'),
    hidden: true
  },
  {
    path: '/register',
    component: () => import('@/views/register'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/error/404'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/error/401'),
    hidden: true
  },
  {
    path: '/user',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'profile',
        component: () => import('@/views/system/user/profile/index'),
        name: 'Profile',
        meta: { title: '个人中心', icon: 'user' }
      }
    ]
  },

  // 主要功能页面（按顺序排列）
  {
    path: '',
    component: Layout,
    redirect: 'index',
    children: [
      {
        path: 'index',
        component: () => import('@/views/dashboard/index'),
        name: 'Index',
        meta: { title: '首页', icon: 'dashboard', affix: true }
      }
    ]
  },
  {
    path: '/search-results',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'index',
        component: () => import('@/views/search-results/index'),
        name: 'SearchResults',
        meta: { title: '搜索结果', icon: 'search' }
      }
    ]
  },
  {
    path: '/info-summary',
    component: Layout,
    redirect: '/info-summary/index',
    children: [
      {
        path: 'index',
        component: () => import('@/views/info-summary/index'),
        name: 'InfoSummary',
        meta: { title: '信息汇总', icon: 'form' }
      }
    ]
  },
  {
    path: '/spread-analysis',
    component: Layout,
    redirect: '/spread-analysis/index',
    children: [
      {
        path: 'index',
        component: () => import('@/views/spread-analysis/index'),
        name: 'SpreadAnalysis',
        meta: { title: '传播分析', icon: 'chart' }
      }
    ]
  },
  {
    path: '/warning-center',
    component: Layout,
    redirect: '/warning-center/index',
    children: [
      {
        path: 'index',
        component: () => import('@/views/warning-center/index'),
        name: 'WarningCenter',
        meta: { title: '预警中心', icon: 'alert' }
      }
    ]
  },
  {
    path: '/report-center',
    component: Layout,
    redirect: '/report-center/index',
    children: [
      {
        path: 'index',
        component: () => import('@/views/report-center/index'),
        name: 'ReportCenter',
        meta: { title: '报告中心', icon: 'report' }
      }
    ]
  },
  {
    path: '/opinion-overview',
    component: Layout,
    redirect: '/opinion-overview/index',
    children: [
      {
        path: 'index',
        component: () => import('@/views/opinion-overview/index'),
        name: 'OpinionOverview',
        meta: { title: '舆情总览', icon: 'monitor' }
      }
    ]
  },
  {
    path: '/my-issues',
    component: Layout,
    redirect: '/my-issues/index',
    children: [
      {
        path: 'index',
        component: () => import('@/views/my-issues/index'),
        name: 'MyIssues',
        meta: { title: '我的问题', icon: 'question' }
      }
    ]
  },
  {
    path: '/event-analysis',
    component: Layout,
    redirect: '/event-analysis/index',
    children: [
      {
        path: 'index',
        component: () => import('@/views/event-analysis/index'),
        name: 'EventAnalysis',
        meta: { title: '事件分析', icon: 'international' }
      }
    ]
  },
  {
    path: '/source-monitoring',
    component: Layout,
    redirect: '/source-monitoring/index',
    children: [
      {
        path: 'index',
        component: () => import('@/views/source-monitoring/index'),
        name: 'SourceMonitoring',
        meta: { title: '信源监测', icon: 'monitor' }
      }
    ]
  },
  {
    path: '/hot-events',
    component: Layout,
    redirect: '/hot-events/index',
    children: [
      {
        path: 'index',
        component: () => import('@/views/hot-events/index'),
        name: 'HotEvents',
        meta: { title: '热点事件', icon: 'hot' }
      }
    ]
  },
  {
    path: '/meta-search',
    component: Layout,
    redirect: '/meta-search/index',
    children: [
      {
        path: 'index',
        component: () => import('@/views/meta-search/index'),
        name: 'MetaSearch',
        meta: { title: '搜索中心', icon: 'search' }
      }
    ]
  },
  {
    path: '/opinion-analysis',
    component: Layout,
    redirect: '/opinion-analysis/index',
    children: [
      {
        path: 'index',
        component: () => import('@/views/opinion-analysis/index'),
        name: 'OpinionAnalysis',
        meta: { title: '舆情分析', icon: 'data-analysis' }
      }
    ]
  },
  {
    path: '/account',
    component: Layout,
    redirect: '/account/index',
    meta: { title: '账号相关', icon: 'user' },
    children: [
      {
        path: 'index',
        component: () => import('@/views/account/index'),
        name: 'Account',
        meta: { title: '我的账号', icon: 'user' }
      },
      {
        path: 'user-management',
        component: () => import('@/views/account/user-management'),
        name: 'UserManagement',
        meta: { title: '用户管理', icon: 'peoples' }
      }
    ]
  }
]

// 动态路由，基于用户权限动态去加载
export const dynamicRoutes = [
  {
    path: '/system/user-auth',
    component: Layout,
    hidden: true,
    permissions: ['system:user:edit'],
    children: [
      {
        path: 'role/:userId(\\d+)',
        component: () => import('@/views/system/user/authRole'),
        name: 'AuthRole',
        meta: { title: '分配角色', activeMenu: '/system/user' }
      }
    ]
  },
  {
    path: '/system/role-auth',
    component: Layout,
    hidden: true,
    permissions: ['system:role:edit'],
    children: [
      {
        path: 'user/:roleId(\\d+)',
        component: () => import('@/views/system/role/authUser'),
        name: 'AuthUser',
        meta: { title: '分配用户', activeMenu: '/system/role' }
      }
    ]
  },
  {
    path: '/system/dict-data',
    component: Layout,
    hidden: true,
    permissions: ['system:dict:list'],
    children: [
      {
        path: 'index/:dictId(\\d+)',
        component: () => import('@/views/system/dict/data'),
        name: 'Data',
        meta: { title: '字典数据', activeMenu: '/system/dict' }
      }
    ]
  },
  {
    path: '/monitor/job-log',
    component: Layout,
    hidden: true,
    permissions: ['monitor:job:list'],
    children: [
      {
        path: 'index/:jobId(\\d+)',
        component: () => import('@/views/monitor/job/log'),
        name: 'JobLog',
        meta: { title: '调度日志', activeMenu: '/monitor/job' }
      }
    ]
  },
  {
    path: '/tool/gen-edit',
    component: Layout,
    hidden: true,
    permissions: ['tool:gen:edit'],
    children: [
      {
        path: 'index/:tableId(\\d+)',
        component: () => import('@/views/tool/gen/editTable'),
        name: 'GenEdit',
        meta: { title: '修改生成配置', activeMenu: '/tool/gen' }
      }
    ]
  }
]

// 防止连续点击多次路由报错
let routerPush = Router.prototype.push;
let routerReplace = Router.prototype.replace;
// push
Router.prototype.push = function push(location) {
  return routerPush.call(this, location).catch(err => err)
}
// replace
Router.prototype.replace = function push(location) {
  return routerReplace.call(this, location).catch(err => err)
}

export default new Router({
  mode: 'history', // 去掉url中的#
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})
