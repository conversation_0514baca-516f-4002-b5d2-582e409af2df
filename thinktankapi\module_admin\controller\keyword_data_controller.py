from datetime import datetime
from fastapi import APIRouter, Depends, Request
from pydantic_validation_decorator import <PERSON><PERSON>te<PERSON>ields
from sqlalchemy.ext.asyncio import AsyncSession
from config.enums import BusinessType
from config.get_db import get_db
from module_admin.annotation.log_annotation import Log
from module_admin.aspect.interface_auth import CheckUserInterfaceAuth
from module_admin.entity.vo.keyword_data_vo import (
    DeleteKeywordDataModel, 
    KeywordDataModel, 
    KeywordDataPageQueryModel
)
from module_admin.service.keyword_data_service import KeywordDataService
from module_admin.service.login_service import LoginService
from utils.log_util import logger
from utils.page_util import PageResponseModel
from utils.response_util import ResponseUtil
from utils.time_format_util import TimeFormatUtil
from datetime import datetime


keywordDataController = APIRouter(prefix='/admin/keywordData', dependencies=[Depends(LoginService.get_current_user)])

# 公开路由器 - 不需要认证
publicKeywordDataController = APIRouter(prefix='/public/keywordData')


@keywordDataController.get(
    '/list', response_model=PageResponseModel, dependencies=[Depends(CheckUserInterfaceAuth('admin:keywordData:list'))]
)
async def get_keyword_data_list(
    request: Request,
    keyword_data_page_query: KeywordDataPageQueryModel = Depends(KeywordDataPageQueryModel.as_query),
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取关键词数据列表
    """
    # 获取分页数据
    keyword_data_page_query_result = await KeywordDataService.get_keyword_data_list_services(query_db, keyword_data_page_query, is_page=True)
    logger.info('获取关键词数据列表成功')

    return ResponseUtil.success(model_content=keyword_data_page_query_result)


@keywordDataController.get('/statistics')
async def get_type_statistics(
    request: Request,
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取类型统计数据
    """
    statistics_result = await KeywordDataService.get_filter_statistics_services(query_db)
    logger.info('获取类型统计数据成功')

    return ResponseUtil.success(data=statistics_result)


@keywordDataController.get('/filter-statistics')
async def get_filter_statistics(
    request: Request,
    keyword: str = None,
    startDate: str = None,
    endDate: str = None,
    platformTypes: str = None,
    sentimentTypes: str = None,
    infoAttributes: str = None,
    webSite: str = None,
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取筛选统计数据
    """
    # 校验日期参数
    if startDate is not None:
        try:
            # 验证日期格式
            parsed_start_date = TimeFormatUtil.parse_date(startDate)
            if isinstance(parsed_start_date, str):  # 解析失败返回原字符串
                return ResponseUtil.error(msg="开始日期格式不正确，请使用YYYY-MM-DD格式")
        except Exception as e:
            logger.error(f"开始日期解析失败: {startDate}, 错误: {str(e)}")
            return ResponseUtil.error(msg="开始日期格式不正确，请使用YYYY-MM-DD格式")
    
    if endDate is not None:
        try:
            # 验证日期格式
            parsed_end_date = TimeFormatUtil.parse_date(endDate)
            if isinstance(parsed_end_date, str):  # 解析失败返回原字符串
                return ResponseUtil.error(msg="结束日期格式不正确，请使用YYYY-MM-DD格式")
        except Exception as e:
            logger.error(f"结束日期解析失败: {endDate}, 错误: {str(e)}")
            return ResponseUtil.error(msg="结束日期格式不正确，请使用YYYY-MM-DD格式")
    
    # 校验日期逻辑关系
    if startDate is not None and endDate is not None:
        try:
            start_dt = datetime.strptime(startDate, '%Y-%m-%d')
            end_dt = datetime.strptime(endDate, '%Y-%m-%d')
            if start_dt > end_dt:
                return ResponseUtil.error(msg="开始日期不能大于结束日期")
            
            # 校验日期范围（可选：限制查询范围，比如不超过1年）
            date_diff = (end_dt - start_dt).days
            if date_diff > 365:
                return ResponseUtil.error(msg="查询时间范围不能超过365天")
        except ValueError as e:
            logger.error(f"日期比较失败: startDate={startDate}, endDate={endDate}, 错误: {str(e)}")
            return ResponseUtil.error(msg="日期格式错误")
    
    # 处理数组参数
    platform_types_list = platformTypes.split(',') if platformTypes else None
    sentiment_types_list = sentimentTypes.split(',') if sentimentTypes else None
    info_attributes_list = infoAttributes.split(',') if infoAttributes else None
    
    statistics_result = await KeywordDataService.get_filter_statistics_services(
        query_db,
        keyword=keyword,
        start_date=startDate,
        end_date=endDate,
        platform_types=platform_types_list,
        sentiment_types=sentiment_types_list,
        info_attributes=info_attributes_list,
        web_site=webSite
    )
    logger.info('获取筛选统计数据成功')

    return ResponseUtil.success(data=statistics_result)


# 公开接口 - 不需要认证
@publicKeywordDataController.get('/list')
async def get_keyword_data_list_public(
    keyword_data_page_query: KeywordDataPageQueryModel = Depends(KeywordDataPageQueryModel.as_query),
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取关键词数据列表 - 公开接口（用于测试）
    """
    from module_admin.entity.vo.keyword_data_vo import KeywordDataPageQueryModel

    try:
        # 处理时间筛选逻辑
        from datetime import datetime, timedelta
        
        actual_start_date = keyword_data_page_query.begin_time
        actual_end_date = keyword_data_page_query.end_time
        
        if keyword_data_page_query.time_filter and not keyword_data_page_query.begin_time and not keyword_data_page_query.end_time:
            today = datetime.now().date()
            if keyword_data_page_query.time_filter == 'today':
                actual_start_date = today.strftime('%Y-%m-%d')
                actual_end_date = today.strftime('%Y-%m-%d')
            elif keyword_data_page_query.time_filter == 'yesterday':
                yesterday = today - timedelta(days=1)
                actual_start_date = yesterday.strftime('%Y-%m-%d')
                actual_end_date = yesterday.strftime('%Y-%m-%d')
            elif keyword_data_page_query.time_filter == 'before_yesterday':
                before_yesterday = today - timedelta(days=2)
                actual_start_date = before_yesterday.strftime('%Y-%m-%d')
                actual_end_date = before_yesterday.strftime('%Y-%m-%d')
            elif keyword_data_page_query.time_filter == 'earlier':
                # 更早：3天前及以前的数据
                earlier_date = today - timedelta(days=3)
                actual_end_date = earlier_date.strftime('%Y-%m-%d')
                # actual_start_date 保持为 None，表示没有开始时间限制
            
            # 更新查询模型的时间参数
            keyword_data_page_query.begin_time = actual_start_date
            keyword_data_page_query.end_time = actual_end_date

        logger.info(f'查询参数: pageNum={keyword_data_page_query.page_num}, pageSize={keyword_data_page_query.page_size}, timeFilter={keyword_data_page_query.time_filter}, actual_start_date={actual_start_date}, actual_end_date={actual_end_date}')

        # 获取分页数据
        keyword_data_page_query_result = await KeywordDataService.get_keyword_data_list_services(query_db, keyword_data_page_query, is_page=True)
        logger.info(f'查询结果类型: {type(keyword_data_page_query_result)}')
        logger.info(f'查询结果: {keyword_data_page_query_result}')

        # 确保返回正确的格式
        if hasattr(keyword_data_page_query_result, 'records'):
            return ResponseUtil.success(data={
                'rows': keyword_data_page_query_result.records,
                'total': keyword_data_page_query_result.total
            })
        else:
            # 如果不是分页对象，直接返回列表
            return ResponseUtil.success(data={
                'rows': keyword_data_page_query_result,
                'total': len(keyword_data_page_query_result) if isinstance(keyword_data_page_query_result, list) else 0
            })
    except Exception as e:
        logger.error(f'获取关键词数据列表失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取数据失败: {str(e)}')


@publicKeywordDataController.get('/filter-statistics')
async def get_filter_statistics_public(
    request: Request,
    keyword: str = None,
    startDate: str = None,
    endDate: str = None,
    platformTypes: str = None,
    sentimentTypes: str = None,
    infoAttributes: str = None,
    webSite: str = None,
    typeField: str = None,
    timeFilter: str = None,
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取筛选统计数据 - 公开接口（用于前端）
    """
    try:
        # 处理数组参数
        platform_types_list = platformTypes.split(',') if platformTypes else None
        sentiment_types_list = sentimentTypes.split(',') if sentimentTypes else None
        info_attributes_list = infoAttributes.split(',') if infoAttributes else None
        
        # 处理时间参数（与主接口保持一致）
        actual_start_date = startDate
        actual_end_date = endDate
        
        if timeFilter and not startDate and not endDate:
            from datetime import datetime, timedelta
            today = datetime.now().date()
            if timeFilter == 'today':
                actual_start_date = today.strftime('%Y-%m-%d')
                actual_end_date = today.strftime('%Y-%m-%d')
            elif timeFilter == 'yesterday':
                yesterday = today - timedelta(days=1)
                actual_start_date = yesterday.strftime('%Y-%m-%d')
                actual_end_date = yesterday.strftime('%Y-%m-%d')
            elif timeFilter == 'before_yesterday':
                before_yesterday = today - timedelta(days=2)
                actual_start_date = before_yesterday.strftime('%Y-%m-%d')
                actual_end_date = before_yesterday.strftime('%Y-%m-%d')
            elif timeFilter == 'earlier':
                # 更早：3天前及以前的数据
                earlier_date = today - timedelta(days=3)
                actual_end_date = earlier_date.strftime('%Y-%m-%d')
                # actual_start_date 保持为 None，表示没有开始时间限制
        
        filter_statistics_result = await KeywordDataService.get_filter_statistics_services(
            query_db,
            keyword=keyword,
            start_date=actual_start_date,
            end_date=actual_end_date,
            platform_types=platform_types_list,
            sentiment_types=sentiment_types_list,
            info_attributes=info_attributes_list,
            web_site=webSite,
            type_field=typeField
        )
        logger.info('获取筛选统计数据成功')
        return ResponseUtil.success(data=filter_statistics_result)
    except Exception as e:
        logger.error(f'获取筛选统计数据失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取筛选统计数据失败: {str(e)}')


@publicKeywordDataController.get('/web-statistics')
async def get_web_statistics_public(
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取网站统计数据 - 公开接口（用于前端菜单）
    """
    try:
        # 调用服务层获取网站统计数据
        web_statistics_result = await KeywordDataService.get_web_statistics_services(query_db)
        logger.info('获取网站统计数据成功')
        return ResponseUtil.success(data=web_statistics_result)
    except Exception as e:
        logger.error(f'获取网站统计数据失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取网站统计数据失败: {str(e)}')


@keywordDataController.post('', dependencies=[Depends(CheckUserInterfaceAuth('admin:keywordData:add'))])
@ValidateFields(validate_model='add_keyword_data')
@Log(title='关键词数据', business_type=BusinessType.INSERT)
async def add_keyword_data(
    request: Request,
    add_keyword_data: KeywordDataModel,
    query_db: AsyncSession = Depends(get_db),
):
    """
    新增关键词数据
    """
    add_keyword_data.createtime = datetime.now()
    add_keyword_data_result = await KeywordDataService.add_keyword_data_services(query_db, add_keyword_data)
    logger.info(add_keyword_data_result.message)

    return ResponseUtil.success(msg=add_keyword_data_result.message)


@keywordDataController.put('', dependencies=[Depends(CheckUserInterfaceAuth('admin:keywordData:edit'))])
@ValidateFields(validate_model='edit_keyword_data')
@Log(title='关键词数据', business_type=BusinessType.UPDATE)
async def edit_keyword_data(
    request: Request,
    edit_keyword_data: KeywordDataModel,
    query_db: AsyncSession = Depends(get_db),
):
    """
    编辑关键词数据
    """
    edit_keyword_data_result = await KeywordDataService.edit_keyword_data_services(query_db, edit_keyword_data)
    logger.info(edit_keyword_data_result.message)

    return ResponseUtil.success(msg=edit_keyword_data_result.message)


@keywordDataController.delete('', dependencies=[Depends(CheckUserInterfaceAuth('admin:keywordData:remove'))])
@ValidateFields(validate_model='delete_keyword_data')
@Log(title='关键词数据', business_type=BusinessType.DELETE)
async def delete_keyword_data(
    request: Request,
    delete_keyword_data: DeleteKeywordDataModel,
    query_db: AsyncSession = Depends(get_db),
):
    """
    删除关键词数据
    """
    delete_keyword_data_result = await KeywordDataService.delete_keyword_data_services(query_db, delete_keyword_data)
    logger.info(delete_keyword_data_result.message)

    return ResponseUtil.success(msg=delete_keyword_data_result.message)


@keywordDataController.get(
    '/{keyword_data_id}', response_model=KeywordDataModel, dependencies=[Depends(CheckUserInterfaceAuth('admin:keywordData:query'))]
)
async def query_detail_keyword_data(request: Request, keyword_data_id: int, query_db: AsyncSession = Depends(get_db)):
    """
    获取关键词数据详情
    """
    keyword_data_detail_result = await KeywordDataService.keyword_data_detail_services(query_db, keyword_data_id)
    logger.info(f'获取keyword_data_id为{keyword_data_id}的信息成功')

    return ResponseUtil.success(data=keyword_data_detail_result)
